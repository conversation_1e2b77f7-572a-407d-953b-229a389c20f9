# DuckDB Streaming JSON Reader Extension

A memory-efficient JSON reader extension for DuckDB implemented in Rust using the struson streaming parser.

## Features

- **Streaming JSON Processing**: Memory-efficient parsing that avoids loading entire JSON files into memory
- **Projection Pushdown**: Only parses requested JSON fields, skipping unnecessary data
- **Nested Structure Support**: Handles complex JSON with multiple levels of nesting
- **DuckDB Integration**: Seamless integration with DuckDB's table function system

## Quick Start

### Build the Extension

```shell
make configure
make debug
```

### Load and Use

```python
import duckdb
conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
conn.execute('LOAD "build/debug/streaming_json_reader.duckdb_extension"')

# Query JSON file
result = conn.execute('SELECT * FROM streaming_json_reader("path/to/file.json")').fetchall()
```

### Basic Usage Examples

```sql
-- Select all columns from JSON file
SELECT * FROM streaming_json_reader('data.json');

-- Select specific fields for efficient processing
SELECT metadata FROM streaming_json_reader('nested.json');
```

## Dependencies

- Rust toolchain
- Python3 and Python3-venv
- [Make](https://www.gnu.org/software/make)
- Git

## Building

```shell
# Configure build environment
make configure

# Build debug version
make debug

# Build release version  
make release
```

The build process creates `build/debug/streaming_json_reader.duckdb_extension` which can be loaded into DuckDB.

## Architecture

See [design_decisions.md](design_decisions.md) for detailed architectural decisions and implementation approaches.
