[package]
name = "streaming_json_reader"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[profile.release]
lto = true
strip = true

[[example]]
# crate-type can't be (at the moment) be overriden for specific targets
# src/wasm_lib.rs forwards to src/lib.rs so that we can change from cdylib
# (that is needed while compiling natively) to staticlib (needed since the
# actual linking will be done via emcc
name = "streaming_json_reader"
path = "src/wasm_lib.rs"
crate-type = ["staticlib"]

[dependencies]
duckdb = { version = "1.3.1", features = ["vtab-loadable"] }
duckdb-loadable-macros = "0.1.5"
libduckdb-sys = { version = "1.3.1", features = ["loadable-extension"] }
struson = "0.6.0"
serde_json = "1.0"
thiserror = "1.0"
