# DuckDB Rust API Reference for Complex Types

## Overview

This document catalogs the DuckDB Rust API patterns for handling complex nested types, specifically focusing on STRUCT and LIST combinations that are needed for the streaming JSON extension.

## Key Findings

### Vector Type Hierarchy

```rust
// Core vector types from duckdb-rs/crates/duckdb/src/core/vector.rs
pub struct FlatVector    // For primitive types (i64, f64, String, etc.)
pub struct ListVector    // For LIST types
pub struct StructVector  // For STRUCT types  
pub struct ArrayVector   // For fixed-size arrays
```

### Critical API Methods

#### ListVector Methods for Complex Children

```rust
impl ListVector {
    // WRONG: Returns FlatVector (for primitive elements only)
    pub fn child(&self, capacity: usize) -> FlatVector
    
    // CORRECT: Returns StructVector for STRUCT elements
    pub fn struct_child(&self, capacity: usize) -> StructVector
    
    // Also available for other complex types:
    pub fn list_child(&self) -> ListVector
    pub fn array_child(&self) -> ArrayVector
}
```

#### StructVector Methods for Field Access

```rust
impl StructVector {
    // Get child vector for field at index
    pub fn child(&self, idx: usize, capacity: usize) -> FlatVector
    
    // Get nested STRUCT child
    pub fn struct_vector_child(&self, idx: usize) -> StructVector
    
    // Get LIST child  
    pub fn list_vector_child(&self, idx: usize) -> ListVector
    
    // Get ARRAY child
    pub fn array_vector_child(&self, idx: usize) -> ArrayVector
    
    // Utility methods
    pub fn num_children(&self) -> usize
    pub fn child_name(&self, idx: usize) -> DuckDbString
    pub fn set_null(&mut self, row: usize)
}
```

### Correct Pattern for LIST[STRUCT] Insertion

Based on `duckdb-rs/crates/duckdb/src/vtab/arrow.rs` lines 628 and 985-988:

```rust
// For LIST[STRUCT] types:
let mut list_vector = output.list_vector(col_idx);

// WRONG (what we were doing):
let mut child_vector = list_vector.child(arr.len()); // Returns FlatVector

// CORRECT (what we should do):
let mut struct_vector = list_vector.struct_child(arr.len()); // Returns StructVector

// Then insert STRUCT data:
for (field_idx, field) in fields.iter().enumerate() {
    let mut field_vector = struct_vector.child(field_idx, 1);
    // Insert field data into field_vector
}
```

### STRUCT Insertion Pattern

From arrow.rs `struct_array_to_vector` function (lines 1041-1081):

```rust
fn insert_struct_data(struct_vector: &mut StructVector, data: &JsonObject, fields: &[JsonField]) {
    for (field_idx, field) in fields.iter().enumerate() {
        let field_value = data.get(&field.name).unwrap_or(&serde_json::Value::Null);
        let mut field_vector = struct_vector.child(field_idx, 1);
        
        match &field.value_type {
            JsonValueType::Number => {
                let n = field_value.as_f64().unwrap_or(0.0);
                let data_slice: &mut [f64] = field_vector.as_mut_slice_with_len(1);
                data_slice[0] = n;
            }
            JsonValueType::String => {
                if let Some(s) = field_value.as_str() {
                    field_vector.insert(0, s);
                } else {
                    field_vector.set_null(0);
                }
            }
            JsonValueType::Boolean => {
                // Handle boolean insertion
            }
            JsonValueType::Null => {
                field_vector.set_null(0);
            }
            // Handle nested types recursively...
        }
    }
}
```

## Root Cause Analysis

### The Problem We Had

1. **Type Creation**: Fixed ✅ - Now creates proper `LIST[STRUCT]` instead of `LIST[VARCHAR]`
2. **Vector Access**: **This was the bug** ❌ - Used `list_vector.child()` instead of `list_vector.struct_child()`
3. **Data Insertion**: Pending - Need to implement proper STRUCT field insertion

### Why child_vector was FlatVector

The `ListVector::child()` method is designed for primitive element types and always returns a `FlatVector`. For complex element types like STRUCT, we must use the specialized methods:

- `struct_child()` for STRUCT elements  
- `list_child()` for LIST elements
- `array_child()` for ARRAY elements

## Implementation Strategy

1. **Replace** `list_vector.child(arr.len())` with `list_vector.struct_child(arr.len())`
2. **Implement** STRUCT field insertion using `struct_vector.child(field_idx, 1)`
3. **Handle** each field type appropriately (Number, String, Boolean, Null)
4. **Support** nested structures recursively if needed

## Code Examples from DuckDB Rust Codebase

### Example 1: LIST[STRUCT] handling (arrow.rs:628)
```rust
let out = &mut chunk.list_vector();
struct_array_to_vector(map_array.entries(), &mut out.struct_child(map_array.entries().len()))?;
```

### Example 2: STRUCT field insertion (arrow.rs:1046)
```rust
primitive_array_to_vector(column, &mut out.child(i, array.len()))?;
```

### Example 3: Nested STRUCT handling (arrow.rs:1068-1069)
```rust
let struct_array = as_struct_array(column.as_ref());
let mut struct_vector = out.struct_vector_child(i);
struct_array_to_vector(struct_array, &mut struct_vector)?;
```

## Next Steps

1. Update array insertion code to use `struct_child()` instead of `child()`
2. Implement proper STRUCT field insertion logic
3. Test with the nested JSON data
4. Verify the output shows proper STRUCT values instead of NULL values
