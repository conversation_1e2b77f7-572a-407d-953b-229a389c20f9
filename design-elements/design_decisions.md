# Design Decisions

## Core Architecture

### Unified Recursive Streaming Architecture
- **Implementation**: Single recursive function `insert_json_value_recursive()` handles all JSON value types uniformly
- **Critical Success Factor**: Avoids special-case handling and enables arbitrary nesting depth without hardcoded limits
- **Vector Routing**: Routes to appropriate vector insertion logic based on value type and expected schema

### Pure Struson Streaming
- **Decision**: Complete elimination of serde_json intermediate representations
- **Implementation**: Direct streaming from JSON to DuckDB vectors using only struson parser
- **Memory Efficiency**: Achieves O(row_size) instead of O(file_size) memory usage
- **Temporary Structures**: Use `TempValue` and `NestedTempValue` only for collection during streaming, not full parsing

### DuckDB Vector Management Patterns
- **Arrays of Objects**: Use `list_vector.struct_child(capacity)`
- **Struct Fields**: Use `struct_vector.child(field_idx, capacity)`
- **Multi-dimensional Arrays**: Recursive list vector management with proper capacity allocation
- **Critical**: Each array element must get its own memory location to avoid data duplication

### Schema Discovery Strategy
- **Implementation**: `discover_json_schema()` with recursive type analysis of first element
- **Rationale**: Sufficient for homogeneous data, enables schema-driven processing for type safety
- **Scope**: Handles arbitrary nesting depth without hardcoded limits

## Critical Anti-Patterns to Avoid

### Never Manual Nesting Logic
- **Anti-Pattern**: Manually implementing N levels of nested logic (if depth == 1, if depth == 2, etc.)
- **Correct Approach**: Use recursive helper functions that handle arbitrary depth

### No Massive Functions with Duplicated Patterns
- **Anti-Pattern**: Single large function handling all cases with repeated code patterns
- **Correct Approach**: Break into focused helper functions with clear responsibilities

### Depth Limits are Anti-Patterns
- **Anti-Pattern**: Hardcoded depth limits (5, 10, 20 levels)
- **Correct Approach**: True recursive processing without artificial constraints

### VARCHAR Fallbacks Break Type System
- **Anti-Pattern**: Converting complex JSON types to VARCHAR when processing becomes difficult
- **Correct Approach**: Always use proper DuckDB types (STRUCT, LIST) with recursive vector handling

### Null Placeholders for Valid Data
- **Anti-Pattern**: Setting valid JSON structures to null when processing is complex
- **Correct Approach**: Preserve all valid JSON structures with complete data

## Implementation Patterns That Work

### Recursive Helper Functions
- `collect_nested_array_data_recursive()` - Multi-dimensional array collection
- `insert_nested_array_recursive()` - Multi-dimensional array insertion
- `insert_array_within_struct()` - Arrays within objects handling

### Temporary Value Types for Streaming
- `TempValue` - Basic primitive collection during streaming
- `NestedTempValue` - Recursive array support with `Array(Vec<NestedTempValue>)`

### Schema-Driven Processing
- Use discovered schema to guide vector allocation and type handling
- Ensures type safety and proper memory management

## Type Mapping
- JSON String → DuckDB VARCHAR
- JSON Number → DuckDB DOUBLE
- JSON Boolean → DuckDB BOOLEAN
- JSON Object → DuckDB STRUCT
- JSON Array → DuckDB LIST
- JSON null → DuckDB NULL

## API Design

### Table Function Interface
```sql
SELECT * FROM streaming_json_reader('path/to/file.json')
```

### Root-Level Array Flattening
- Arrays at root level are flattened into separate rows
- `[{"id": 1}, {"id": 2}]` becomes two rows with `id` column
- Matches DuckDB's `read_json_auto()` behavior for compatibility

### Error Handling
- Fail-fast on malformed JSON rather than attempting partial recovery
- Clear failure modes for debugging

## Implementation Status

### Schema Discovery
Use first element schema only, with query-driven column selection.
- Avoids performance penalty of full file scanning
- Users can define schema by selecting named fields
- Enables streaming without lookahead

### Type System Integration
Work towards full DuckDB JSON type compatibility. Use VARCHAR for intermediate versions.
- Should eventually match DuckDB's existing JSON reader API
- Full types including nested types is the end goal

### API Surface
Single parameter API: `streaming_json_reader(file_path)` with query-driven behavior.
- `SELECT *` should match DuckDB's `read_json_auto()` behavior exactly
- Context-preserving flattening via query structure
- Query planner integration determines behavior

## Current Status

### Working Features
- Projection pushdown (skips unrequested fields)
- Error handling for malformed JSON
- Basic JSON parsing and STRUCT creation
- String data types

### Critical Issues (Resolved)
- ✅ STRUCT-within-STRUCT crashes fixed using proper `struct_vector_child()` API
- ✅ Deep nested structures (3+ levels) now working with recursive STRUCT handling
- ✅ Empty JSON objects handled with fallback "json" VARCHAR column
- ✅ Numbers properly stored as DOUBLE type with direct memory access
- ✅ STRUCT field values use correct data types
- ✅ Array processing works for STRUCT arrays
- ✅ Projection pushdown optimization working for both simple and STRUCT fields

### Known Limitations

#### Empty Object Handling
**Current Approach**: Empty JSON objects `{}` are handled by creating a single VARCHAR column named "json" containing the string "{}"

**Built-in Behavior**: DuckDB's `read_json_auto()` returns `MAP(VARCHAR, JSON)` type with actual empty map value

**Rationale for Current Approach**:
- DuckDB Rust API lacks high-level MAP type creation methods
- JsonValueType enum doesn't include MAP variant
- No MapVector equivalent in current Rust wrapper
- VARCHAR approach prevents crashes and satisfies DuckDB's "at least one column" requirement

**Future Enhancement**: Implement proper MAP type support when:
- DuckDB Rust API exposes MAP vector operations
- Extension type system is extended to support MAP types
- Core STRUCT functionality is fully robust

**Technical Debt**: This is a temporary workaround, not a design limitation

### Critical Design Constraints

#### STRUCT Type Integrity (NEVER VIOLATE)
**Forbidden Patterns**:
- ❌ Converting JSON objects to JSON strings for insertion into STRUCT fields
- ❌ Using VARCHAR as a fallback for complex nested structures
- ❌ Compromising the STRUCT type system for convenience
- ❌ Any VARCHAR conversion for STRUCT data (breaks core design principle)

**Required Patterns**:
- ✅ Use proper DuckDB STRUCT types and recursive vector handling
- ✅ Use `struct_vector_child()` for nested STRUCT access
- ✅ Implement recursive STRUCT insertion without depth limits
- ✅ Maintain schema-insertion type consistency

**Acceptable VARCHAR Uses** (Limited Exceptions):
- ✅ Empty object fallback (single "json" column as documented limitation)
- ✅ Primitive string fields that are actually strings in the JSON
- ✅ Temporary null placeholders for unimplemented features (with TODO comments)

#### Implementation Status
**Working STRUCT Features**:
- Basic STRUCT objects with primitive fields
- STRUCT-within-STRUCT (2-3 levels tested)
- STRUCT arrays with proper field access
- Recursive STRUCT vector handling using `struct_vector_child()`
- Deep nested field access (e.g., `company.departments.engineering.head`)

**Remaining Work**:
- Array flattening and complex array operations
- Arrays within deeply nested STRUCTs
- Migration from serde_json to pure struson streaming

### Recursive STRUCT Handling Architecture Options

#### Current Implementation Violations (Critical Issues)
**Code Audit Findings**:
- **34 serde_json instances** violating streaming design principle
- **4 arbitrary depth limits** (15, 2, 20, 5) with no technical justification
- **5 VARCHAR fallback code blocks** converting STRUCT data to strings (FORBIDDEN)
- **Prohibited comments** suggesting "stack overflow prevention" and "JSON string conversion"

**Specific Violation Locations**:
- Lines 629-645: Depth limit 15 with VARCHAR conversion
- Lines 655-672: Depth limit 2 with VARCHAR conversion
- Lines 888-900: Depth limit 20 with VARCHAR conversion
- Lines 949-970: STRUCT to JSON string conversion
- Lines 986-1000: ARRAY to JSON string conversion

#### Architectural Options Analysis

**Option A: Pure Recursive Approach (No Depth Limits)**
```rust
fn insert_struct_recursive(
    struct_vector: &mut StructVector,
    json_reader: &mut JsonStreamReader<File>,
    fields: &[JsonField],
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>>
```

*Technical Details*:
- Direct struson streaming without serde_json conversion
- Recursive calls for nested STRUCTs using struct_vector_child()
- No artificial depth limits or VARCHAR fallbacks
- Memory usage: O(nesting_depth) stack space only

*Pros*:
- ✅ True streaming with minimal memory usage
- ✅ No arbitrary depth limits
- ✅ Proper STRUCT type preservation
- ✅ Clean recursive design matching DuckDB's type system

*Cons*:
- ❌ Potential stack overflow on extremely deep nesting (1000+ levels)
- ❌ More complex error handling across recursive calls

**Option B: Iterative Stack-Based Approach**
```rust
struct StackFrame {
    vector: StructVector,
    fields: Vec<JsonField>,
    field_idx: usize,
}
fn insert_struct_iterative() -> Result<(), Box<dyn std::error::Error>>
```

*Technical Details*:
- Explicit stack management instead of call stack
- struson streaming with manual state tracking
- struct_vector_child() calls managed iteratively
- Memory usage: O(nesting_depth) heap space for stack frames

*Pros*:
- ✅ No stack overflow risk (heap-based stack)
- ✅ True streaming with minimal memory
- ✅ Proper STRUCT type preservation
- ✅ Handles arbitrary depth (limited only by available memory)

*Cons*:
- ❌ More complex implementation and debugging
- ❌ Manual state management complexity

**Option C: Schema-Driven Dynamic Type Resolution**
```rust
fn insert_with_dynamic_schema(
    output: &DataChunkHandle,
    json_reader: &mut JsonStreamReader<File>,
    base_schema: &JsonSchema,
) -> Result<(), Box<dyn std::error::Error>>
```

*Technical Details*:
- On-the-fly schema discovery during insertion
- Dynamic STRUCT type creation using DuckDB API
- struson streaming with adaptive type resolution
- Memory usage: O(schema_complexity + nesting_depth)

*Pros*:
- ✅ Handles unknown nesting patterns
- ✅ True streaming architecture
- ✅ Flexible schema adaptation
- ✅ No pre-determined limitations

*Cons*:
- ❌ Complex schema management and type creation
- ❌ Potential performance overhead from dynamic typing
- ❌ More complex error recovery scenarios

**Option D: Lazy Evaluation with On-Demand Vector Creation**
```rust
fn insert_lazy_vectors(
    output: &DataChunkHandle,
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
) -> Result<(), Box<dyn std::error::Error>>
```

*Technical Details*:
- Vector creation only when data is encountered
- Continuation-passing style for deep nesting
- struson streaming with deferred vector allocation
- Memory usage: O(active_vectors) - minimal until needed

*Pros*:
- ✅ Minimal memory usage (vectors created on demand)
- ✅ True streaming without intermediate storage
- ✅ Efficient for sparse nested structures

*Cons*:
- ❌ Complex continuation management
- ❌ Harder to implement error recovery
- ❌ Potential performance overhead from lazy allocation

**Option E: Hybrid Streaming-Recursive Approach (SELECTED)**
```rust
fn insert_hybrid_streaming(
    output: &DataChunkHandle,
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
) -> Result<(), Box<dyn std::error::Error>>
```

*Technical Details*:
- Pure struson streaming for JSON parsing
- Recursive STRUCT vector creation using struct_vector_child()
- No serde_json conversion or intermediate representation
- Tail-call optimization where possible
- Memory usage: O(nesting_depth) stack + O(1) streaming buffer

*Pros*:
- ✅ Best balance of simplicity and efficiency
- ✅ True streaming architecture
- ✅ Clean recursive design
- ✅ Proper STRUCT type preservation
- ✅ Leverages proven struct_vector_child() pattern

*Cons*:
- ❌ Still potential stack overflow on extreme depth (mitigated by practical limits)
- ❌ Requires careful error propagation through recursion

#### Implementation Decision: Option E (Hybrid Streaming-Recursive) - SUCCESSFULLY IMPLEMENTED

**Implementation Results** (Completed):
- ✅ **All VARCHAR fallbacks removed**: Eliminated 5 prohibited code blocks (lines 629-645, 655-672, 888-900, 949-970, 986-1000)
- ✅ **All arbitrary depth limits removed**: Eliminated 4 different limits (15, 2, 20, 5)
- ✅ **True recursive STRUCT handling**: Implemented `insert_struct_recursive()` for arbitrary depth
- ✅ **Proper STRUCT type preservation**: No STRUCT data converted to VARCHAR strings
- ✅ **Critical design constraints enforced**: Added comprehensive comments preventing regression

**Success Criteria Results**:
- ✅ **Deep nesting capability**: Successfully handles 15+ levels without VARCHAR fallbacks
- ✅ **Memory efficiency**: O(nesting_depth) stack space, not O(data_size)
- ✅ **Existing functionality preserved**: All STRUCT tests continue passing
- ✅ **Type system integrity**: Proper STRUCT types throughout recursion
- ✅ **Performance maintained**: No performance degradation observed

**Stress Test Evidence**:
- ✅ **5-level nesting**: PASSED - Basic recursive functionality
- ✅ **10-level nesting**: PASSED - Stress test validation
- ✅ **15-level nesting**: PASSED - Extreme depth handling
- ✅ **Schema consistency**: PASSED - Type system integrity maintained
- ✅ **STRUCT preservation**: PASSED - No VARCHAR fallbacks detected

**Architecture Validation**:
Option E successfully demonstrates that recursive STRUCT handling can be implemented without:
- Arbitrary depth limitations
- VARCHAR fallbacks for complex data
- Memory efficiency compromises
- Type system violations

**Remaining Work** (Non-Critical):
- ✅ **Array handling within deeply nested STRUCTs**: COMPLETED - Arrays now properly handled in recursive STRUCT contexts
- Complete migration from serde_json to pure struson streaming (Phase 2 optimization)
- Performance optimization for extreme nesting scenarios (if needed)

**Array Handling Implementation** (Completed):
- **Problem Solved**: Arrays within deeply nested STRUCTs were being treated as primitive types and set to NULL
- **Root Cause**: `insert_struct_recursive()` function lumped `JsonValueType::Array` with primitive types in catch-all `_ => {}` pattern
- **Solution**: Added explicit `JsonValueType::Array` handling in recursive STRUCT insertion
- **Implementation**: Uses `list_vector_child()` and recursive calls to `insert_struct_recursive()` for STRUCT array elements
- **Evidence**: Mixed deep nesting test now passes - 5 levels of STRUCT nesting + arrays of STRUCTs working correctly

**Decision Status**: Option E is validated as the correct architectural approach. No migration to alternative options (A-D) is currently needed based on evidence gathered.

### Critical Architectural Lessons Learned

#### Anti-Pattern: Manual Depth Handling (NEVER DO THIS)

**Problem Identified**: The original implementation contained a massive 259-line `insert_structured_value_with_depth()` function that manually handled 4 levels of nesting with hardcoded logic.

**Why This Was Wrong**:
- **Code Duplication**: Each nesting level repeated the same pattern matching logic
- **Arbitrary Limitations**: Only handled exactly 4 levels, failing on deeper valid JSON
- **Maintenance Nightmare**: Changes required updating multiple identical code blocks
- **Performance Overhead**: Unnecessary intermediate processing at each manual level
- **Architectural Violation**: Contradicted the principle of handling arbitrary valid JSON

**Correct Pattern**: Use proper recursion with helper functions like `insert_struct_recursive()` that handle arbitrary depth cleanly.

#### Anti-Pattern: Arbitrary Depth Limits (FORBIDDEN)

**Problem Identified**: Four different arbitrary depth limits were found:
- Depth > 15: "Maximum nesting depth exceeded"
- Depth > 2: "Deep nesting detected"
- Depth > 20: "Maximum nesting depth exceeded"
- Depth > 5: "Deep nesting detected"

**Why Depth Limits Are Fundamentally Flawed**:
- **No Technical Justification**: Numbers were arbitrary, not based on actual constraints
- **User Experience Violation**: Valid JSON should not fail due to artificial limits
- **Type System Violation**: Forced conversion of STRUCT data to VARCHAR strings
- **Inconsistency**: Four different limits indicated no coherent design
- **Memory Efficiency Violation**: Real JSON can legitimately have deep nesting

**Correct Pattern**: Use proper recursive algorithms that handle depth naturally through stack management, not artificial cutoffs.

#### Anti-Pattern: VARCHAR Fallbacks for STRUCT Data (STRICTLY FORBIDDEN)

**Problem Identified**: Five code blocks converted STRUCT data to VARCHAR strings when encountering "difficult" scenarios.

**Why This Violates Core Design Principles**:
- **Type System Integrity**: DuckDB's STRUCT types provide structured access that VARCHAR cannot
- **Query Capability Loss**: Converting to VARCHAR eliminates the ability to access nested fields
- **Performance Degradation**: String parsing is slower than direct STRUCT field access
- **Data Model Violation**: JSON objects should map to STRUCT types, not strings
- **Regression to Barbarism**: Defeats the entire purpose of structured JSON handling

**Correct Pattern**: Always use proper STRUCT types with recursive vector handling, setting NULL only for genuinely invalid data.

#### Correct Pattern: Recursive Helper Functions

**Solution Implemented**: `insert_struct_recursive()` function that:
- Handles arbitrary nesting depth through natural recursion
- Uses proper DuckDB STRUCT vector operations (`struct_vector_child()`)
- Maintains type system integrity throughout recursion
- Provides clean error handling with graceful NULL fallbacks
- Eliminates code duplication through single implementation

**Evidence of Success**:
- 15+ level nesting: PASSED without modifications
- Memory usage: O(nesting_depth) stack space only
- Performance: No degradation from recursive approach
- Maintainability: Single function to modify for STRUCT handling changes

#### Implementation Quality Metrics

**Before Cleanup**:
- 259-line monster function with 4 levels of manual nesting
- 5 VARCHAR fallback violations
- 4 arbitrary depth limits
- Massive code duplication

**After Cleanup**:
- Clean recursive architecture with single-responsibility functions
- Zero VARCHAR fallbacks for STRUCT data
- Zero arbitrary depth limits
- Successful handling of 15+ nesting levels

**Key Takeaway**: Always prefer proper recursive algorithms over manual depth handling. The complexity should be in the algorithm design, not in hardcoded level management.

#### CRITICAL: Eliminate All "For Now" Implementations (ABSOLUTE PROHIBITION)

**Problem Identified**: Temporary solutions prefixed with "For now" create data corruption and architectural debt in DuckDB extensions.

**Specific Evidence**: The implementation `start_offset = row_idx * estimated_elements_per_row` caused data corruption where row 1 data overwrote row 0 data in multi-dimensional arrays.

**Why "For Now" Is Forbidden in Memory-Critical Systems**:
- **Memory Corruption**: Approximations in memory offsets cause data overwrites and crashes
- **Exponential Debugging Cost**: "For now" solutions become exponentially harder to fix over time
- **Architectural Debt**: Creates incorrect patterns that infect the entire codebase
- **Root Cause Indicator**: "For now" indicates insufficient understanding of DuckDB's exact memory layout requirements

**Correct Response When Tempted to Write "For Now"**:
1. **Stop Immediately**: Do not implement the approximation
2. **Research**: Study DuckDB API patterns in design-elements/ documentation
3. **Understand**: Learn the exact memory layout requirements before proceeding
4. **Implement Properly**: Use exact cumulative offsets based on actual data sizes, not estimates

**Evidence of Damage**: Multi-row list vector processing with estimated offsets caused systematic data corruption that took significant debugging effort to identify and fix properly.

