# Schema Inference Architecture Design

## Overview

This document defines the architecture for a two-pass schema inference system that solves DuckDB's JSON extension memory pressure problems. The system uses streaming JSON parsing to infer complete schemas and calculate exact vector capacities before data loading.

## Core Design Principles

### Memory Efficiency Goals
- **Target**: O(schema_complexity + vector_batch_size) memory usage vs current O(file_size)
- **Specific**: Process 25GB JSON files with <100MB peak memory usage
- **Approach**: Two-pass system eliminates need to hold entire JSON in memory

### Architectural Constraints
- **Pure Struson Streaming**: No serde_json intermediate representations
- **Recursive Design**: Handle arbitrary nesting depth without hardcoded limits
- **Exact Calculations**: No approximations or "For Now" implementations
- **Type System Integrity**: Always use proper DuckDB STRUCT/LIST types

## Schema Representation System

### Core Data Structures

```rust
/// Represents the complete inferred schema with statistics
#[derive(Debug, <PERSON>lone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub statistics: SchemaStatistics,
    pub memory_requirements: MemoryRequirements,
}

/// Recursive JSON type representation with capacity information
#[derive(Debug, <PERSON>lone)]
pub enum InferredJsonType {
    Null,
    Boolean,
    Number,
    String { 
        max_length: Option<usize>,
        total_instances: usize,
    },
    Array { 
        element_type: Box<InferredJsonType>,
        max_length: Option<usize>,
        total_elements: usize,
        array_count: usize,
    },
    Object { 
        fields: HashMap<String, InferredJsonType>,
        is_homogeneous: bool,
        total_instances: usize,
    },
}

/// Schema statistics for memory planning and validation
#[derive(Debug, Clone)]
pub struct SchemaStatistics {
    pub total_rows: usize,
    pub max_nesting_depth: usize,
    pub unique_field_names: HashSet<String>,
    pub schema_complexity_score: usize,
}

/// Exact memory requirements calculated from schema
#[derive(Debug, Clone)]
pub struct MemoryRequirements {
    pub vector_capacities: HashMap<VectorPath, usize>,
    pub total_memory_estimate: usize,
    pub peak_memory_estimate: usize,
}

/// Identifies specific vectors in the nested structure
#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct VectorPath {
    pub path_components: Vec<PathComponent>,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum PathComponent {
    Root,
    ArrayElement,
    ObjectField(String),
}
```

### Memory Bounds Analysis

#### Schema Complexity Calculation
```rust
impl InferredJsonType {
    /// Calculate memory complexity score for this type
    pub fn calculate_complexity(&self) -> usize {
        match self {
            InferredJsonType::Null | 
            InferredJsonType::Boolean | 
            InferredJsonType::Number => 1,
            
            InferredJsonType::String { max_length, .. } => {
                // String complexity based on maximum length
                max_length.unwrap_or(100).min(1000) // Cap at reasonable limit
            },
            
            InferredJsonType::Array { element_type, array_count, .. } => {
                // Array complexity: base cost + element complexity * array count
                10 + element_type.calculate_complexity() * array_count.min(1000)
            },
            
            InferredJsonType::Object { fields, total_instances, .. } => {
                // Object complexity: field count + sum of field complexities
                let field_complexity: usize = fields.values()
                    .map(|field_type| field_type.calculate_complexity())
                    .sum();
                fields.len() * 5 + field_complexity * total_instances.min(1000)
            },
        }
    }
}
```

#### Memory Bounds Enforcement
- **Schema Complexity Limit**: 10MB maximum for any inferred schema
- **Field Count Limit**: 10,000 unique field names maximum
- **Nesting Depth Tracking**: No artificial limits, but track for memory estimation
- **Early Termination**: Stop schema inference if bounds exceeded

## Schema Inference Engine

### Streaming Schema Discovery

```rust
/// Core schema inference engine using pure struson streaming
pub struct SchemaInferrer {
    current_schema: Option<InferredJsonType>,
    nesting_stack: Vec<InferenceContext>,
    statistics: SchemaStatistics,
    memory_tracker: MemoryUsageTracker,
}

/// Context for tracking inference state at each nesting level
#[derive(Debug)]
struct InferenceContext {
    context_type: ContextType,
    field_schemas: HashMap<String, InferredJsonType>,
    element_count: usize,
    max_array_length: usize,
}

#[derive(Debug)]
enum ContextType {
    RootLevel,
    ArrayElement,
    ObjectField(String),
}

impl SchemaInferrer {
    /// Infer schema from streaming JSON reader
    pub fn infer_from_stream(
        &mut self, 
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        self.memory_tracker.start_monitoring();
        
        let inferred_type = self.infer_value_recursive(json_reader)?;
        
        // Validate memory bounds
        let complexity = inferred_type.calculate_complexity();
        if complexity > MAX_SCHEMA_COMPLEXITY {
            return Err(SchemaInferenceError::ComplexityExceeded(complexity));
        }
        
        Ok(inferred_type)
    }
    
    /// Recursive schema inference for arbitrary nesting depth
    fn infer_value_recursive(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                Ok(InferredJsonType::Null)
            },
            
            struson::reader::ValueType::Boolean => {
                json_reader.next_bool()?;
                Ok(InferredJsonType::Boolean)
            },
            
            struson::reader::ValueType::Number => {
                json_reader.next_number_as_str()?;
                Ok(InferredJsonType::Number)
            },
            
            struson::reader::ValueType::String => {
                let string_value = json_reader.next_string()?;
                Ok(InferredJsonType::String {
                    max_length: Some(string_value.len()),
                    total_instances: 1,
                })
            },
            
            struson::reader::ValueType::Array => {
                self.infer_array_schema(json_reader)
            },
            
            struson::reader::ValueType::Object => {
                self.infer_object_schema(json_reader)
            },
        }
    }
}
```

## Vector Capacity Calculator

### Exact Capacity Calculation

```rust
/// Calculates exact DuckDB vector capacities from inferred schema
pub struct VectorCapacityCalculator {
    schema: InferredSchema,
    row_count: usize,
}

impl VectorCapacityCalculator {
    /// Calculate exact capacities for all vectors in the schema
    pub fn calculate_all_capacities(&self) -> Result<VectorCapacities, CapacityError> {
        let mut capacities = HashMap::new();
        let root_path = VectorPath { path_components: vec![PathComponent::Root] };
        
        self.calculate_type_capacity(
            &self.schema.root_type,
            &root_path,
            self.row_count,
            &mut capacities
        )?;
        
        Ok(VectorCapacities {
            capacities,
            total_memory: self.calculate_total_memory(&capacities),
        })
    }
}
```

## Configuration and Constants

### Memory and Performance Limits

```rust
/// Maximum schema complexity score (approximately 10MB of schema data)
const MAX_SCHEMA_COMPLEXITY: usize = 10_000_000;

/// Maximum unique field names across entire schema
const MAX_UNIQUE_FIELD_NAMES: usize = 10_000;

/// Maximum array elements to analyze during inference (for performance)
const MAX_ARRAY_ELEMENTS_FOR_INFERENCE: usize = 1000;

/// Default memory limit for schema inference (100MB)
const DEFAULT_MEMORY_LIMIT: usize = 100 * 1024 * 1024;

/// Configuration for schema inference behavior
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub max_memory_usage: Option<usize>,
    pub max_schema_complexity: usize,
    pub max_unique_fields: usize,
    pub enable_progress_reporting: bool,
    pub enable_debug_output: bool,
}

impl Default for SchemaInferenceConfig {
    fn default() -> Self {
        Self {
            max_memory_usage: Some(DEFAULT_MEMORY_LIMIT),
            max_schema_complexity: MAX_SCHEMA_COMPLEXITY,
            max_unique_fields: MAX_UNIQUE_FIELD_NAMES,
            enable_progress_reporting: false,
            enable_debug_output: false,
        }
    }
}
```

## Detailed Implementation Patterns

### Array Schema Inference

```rust
impl SchemaInferrer {
    /// Infer schema for JSON arrays with element type analysis
    fn infer_array_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_array()?;

        let mut element_type: Option<InferredJsonType> = None;
        let mut element_count = 0;
        let mut total_elements = 0;

        // Push array context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::ArrayElement,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next()? {
            let current_element = self.infer_value_recursive(json_reader)?;
            element_count += 1;
            total_elements += current_element.count_total_elements();

            element_type = Some(match element_type {
                None => current_element,
                Some(existing) => self.merge_types(existing, current_element)?,
            });

            // Check memory bounds during inference
            if element_count > MAX_ARRAY_ELEMENTS_FOR_INFERENCE {
                break; // Stop inferring after reasonable sample
            }
        }

        json_reader.end_array()?;

        // Pop array context and update statistics
        if let Some(mut context) = self.nesting_stack.pop() {
            context.element_count = element_count;
            context.max_array_length = element_count;
        }

        Ok(InferredJsonType::Array {
            element_type: Box::new(element_type.unwrap_or(InferredJsonType::Null)),
            max_length: Some(element_count),
            total_elements,
            array_count: 1,
        })
    }

    /// Infer schema for JSON objects with field analysis
    fn infer_object_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_object()?;

        let mut fields = HashMap::new();
        let mut field_count = 0;

        // Push object context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::RootLevel,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next()? {
            let field_name = json_reader.next_name()?;
            let field_type = self.infer_value_recursive(json_reader)?;

            fields.insert(field_name.clone(), field_type);
            field_count += 1;

            // Track unique field names for memory bounds
            self.statistics.unique_field_names.insert(field_name);

            // Check field count bounds
            if self.statistics.unique_field_names.len() > MAX_UNIQUE_FIELD_NAMES {
                return Err(SchemaInferenceError::TooManyFields(
                    self.statistics.unique_field_names.len()
                ));
            }
        }

        json_reader.end_object()?;

        // Pop object context
        self.nesting_stack.pop();

        Ok(InferredJsonType::Object {
            fields,
            is_homogeneous: true, // Will be determined during merging
            total_instances: 1,
        })
    }
}
```

### Vector Capacity Calculation Details

```rust
impl VectorCapacityCalculator {
    /// Recursive capacity calculation for nested types
    fn calculate_type_capacity(
        &self,
        json_type: &InferredJsonType,
        current_path: &VectorPath,
        instance_count: usize,
        capacities: &mut HashMap<VectorPath, usize>
    ) -> Result<(), CapacityError> {
        match json_type {
            InferredJsonType::Array { element_type, total_elements, .. } => {
                // List vector needs capacity for all elements across all arrays
                capacities.insert(current_path.clone(), *total_elements * instance_count);

                // Calculate capacity for child elements
                let child_path = current_path.append(PathComponent::ArrayElement);
                self.calculate_type_capacity(
                    element_type,
                    &child_path,
                    *total_elements * instance_count,
                    capacities
                )?;
            },

            InferredJsonType::Object { fields, total_instances, .. } => {
                // Struct vector capacity is number of struct instances
                capacities.insert(current_path.clone(), *total_instances * instance_count);

                // Calculate capacity for each field
                for (field_name, field_type) in fields {
                    let field_path = current_path.append(PathComponent::ObjectField(field_name.clone()));
                    self.calculate_type_capacity(
                        field_type,
                        &field_path,
                        *total_instances * instance_count,
                        capacities
                    )?;
                }
            },

            InferredJsonType::String { total_instances, .. } |
            InferredJsonType::Number { total_instances, .. } |
            InferredJsonType::Boolean { total_instances, .. } => {
                // Primitive types: capacity is number of instances
                capacities.insert(current_path.clone(), *total_instances * instance_count);
            },

            InferredJsonType::Null => {
                // Null values don't need capacity
                capacities.insert(current_path.clone(), 0);
            },
        }

        Ok(())
    }

    /// Calculate total memory requirement from all capacities
    fn calculate_total_memory(&self, capacities: &HashMap<VectorPath, usize>) -> usize {
        capacities.values().sum::<usize>() * 8 // Assume 8 bytes per element average
    }
}

/// Container for all calculated vector capacities
#[derive(Debug, Clone)]
pub struct VectorCapacities {
    pub capacities: HashMap<VectorPath, usize>,
    pub total_memory: usize,
}

impl VectorPath {
    /// Append a path component to create a new path
    pub fn append(&self, component: PathComponent) -> VectorPath {
        let mut new_components = self.path_components.clone();
        new_components.push(component);
        VectorPath { path_components: new_components }
    }

    /// Convert path to human-readable string for debugging
    pub fn to_string(&self) -> String {
        self.path_components.iter()
            .map(|component| match component {
                PathComponent::Root => "root".to_string(),
                PathComponent::ArrayElement => "[]".to_string(),
                PathComponent::ObjectField(name) => format!(".{}", name),
            })
            .collect::<Vec<_>>()
            .join("")
    }
}
```

## Error Handling and Validation

### Comprehensive Error Types

```rust
#[derive(Debug, thiserror::Error)]
pub enum SchemaInferenceError {
    #[error("File I/O error: {0}")]
    FileError(#[from] std::io::Error),

    #[error("JSON parsing error at position {position}: {message}")]
    JsonError { position: usize, message: String },

    #[error("Schema complexity exceeded limit: {0} > {}", MAX_SCHEMA_COMPLEXITY)]
    ComplexityExceeded(usize),

    #[error("Too many unique field names: {0} > {}", MAX_UNIQUE_FIELD_NAMES)]
    TooManyFields(usize),

    #[error("Memory allocation failed: {0}")]
    MemoryError(String),

    #[error("Type merging conflict: cannot merge {type1:?} with {type2:?}")]
    TypeMergeConflict { type1: InferredJsonType, type2: InferredJsonType },
}

#[derive(Debug, thiserror::Error)]
pub enum CapacityError {
    #[error("Capacity calculation overflow for path {path}")]
    CapacityOverflow { path: String },

    #[error("Invalid capacity: {capacity} for type {type_name}")]
    InvalidCapacity { capacity: usize, type_name: String },
}
```

### Memory Usage Tracking

```rust
/// Memory usage tracking and bounds enforcement
pub struct MemoryUsageTracker {
    baseline_memory: usize,
    peak_memory: usize,
    current_memory: usize,
    memory_limit: Option<usize>,
    checkpoints: Vec<MemoryCheckpoint>,
}

#[derive(Debug, Clone)]
struct MemoryCheckpoint {
    name: String,
    memory_usage: usize,
    timestamp: std::time::Instant,
}

impl MemoryUsageTracker {
    pub fn new(memory_limit: Option<usize>) -> Self {
        Self {
            baseline_memory: 0,
            peak_memory: 0,
            current_memory: 0,
            memory_limit,
            checkpoints: Vec::new(),
        }
    }

    pub fn start_monitoring(&mut self) {
        self.baseline_memory = self.get_current_memory_usage();
        self.current_memory = self.baseline_memory;
        self.peak_memory = self.baseline_memory;
        self.record_checkpoint("baseline");
    }

    pub fn check_memory_bounds(&mut self) -> Result<(), SchemaInferenceError> {
        self.current_memory = self.get_current_memory_usage();
        self.peak_memory = self.peak_memory.max(self.current_memory);

        if let Some(limit) = self.memory_limit {
            if self.current_memory > limit {
                return Err(SchemaInferenceError::MemoryError(
                    format!("Memory usage {} exceeds limit {}", self.current_memory, limit)
                ));
            }
        }

        Ok(())
    }

    pub fn record_checkpoint(&mut self, name: &str) {
        self.current_memory = self.get_current_memory_usage();
        self.checkpoints.push(MemoryCheckpoint {
            name: name.to_string(),
            memory_usage: self.current_memory,
            timestamp: std::time::Instant::now(),
        });
    }

    pub fn get_memory_report(&self) -> MemoryReport {
        MemoryReport {
            baseline_memory: self.baseline_memory,
            peak_memory: self.peak_memory,
            current_memory: self.current_memory,
            memory_delta: self.current_memory.saturating_sub(self.baseline_memory),
            checkpoints: self.checkpoints.clone(),
        }
    }

    fn get_current_memory_usage(&self) -> usize {
        // Platform-specific memory usage detection
        #[cfg(target_os = "linux")]
        {
            self.get_linux_memory_usage()
        }
        #[cfg(not(target_os = "linux"))]
        {
            // Fallback: estimate based on heap allocations
            0 // Placeholder - would need platform-specific implementation
        }
    }

    #[cfg(target_os = "linux")]
    fn get_linux_memory_usage(&self) -> usize {
        use std::fs;
        if let Ok(status) = fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<usize>() {
                            return kb * 1024; // Convert KB to bytes
                        }
                    }
                }
            }
        }
        0
    }
}

#[derive(Debug, Clone)]
pub struct MemoryReport {
    pub baseline_memory: usize,
    pub peak_memory: usize,
    pub current_memory: usize,
    pub memory_delta: usize,
    pub checkpoints: Vec<MemoryCheckpoint>,
}
```

## Schema Merging for Heterogeneous Data

### Type Merging Implementation

```rust
impl SchemaInferrer {
    /// Merge two JSON types to handle heterogeneous arrays/objects
    fn merge_types(
        &self,
        type1: InferredJsonType,
        type2: InferredJsonType
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match (type1, type2) {
            // Same types: merge statistics
            (InferredJsonType::String { max_length: len1, total_instances: count1 },
             InferredJsonType::String { max_length: len2, total_instances: count2 }) => {
                Ok(InferredJsonType::String {
                    max_length: Some(len1.unwrap_or(0).max(len2.unwrap_or(0))),
                    total_instances: count1 + count2,
                })
            },

            // Array types: merge element types and statistics
            (InferredJsonType::Array { element_type: elem1, total_elements: total1, array_count: count1, .. },
             InferredJsonType::Array { element_type: elem2, total_elements: total2, array_count: count2, .. }) => {
                let merged_element = self.merge_types(*elem1, *elem2)?;
                Ok(InferredJsonType::Array {
                    element_type: Box::new(merged_element),
                    max_length: None, // Will be calculated during capacity planning
                    total_elements: total1 + total2,
                    array_count: count1 + count2,
                })
            },

            // Object types: merge field schemas
            (InferredJsonType::Object { fields: fields1, total_instances: count1, .. },
             InferredJsonType::Object { fields: fields2, total_instances: count2, .. }) => {
                let mut merged_fields = fields1;

                for (field_name, field_type2) in fields2 {
                    match merged_fields.get(&field_name) {
                        Some(field_type1) => {
                            // Merge existing field
                            let merged_field = self.merge_types(field_type1.clone(), field_type2)?;
                            merged_fields.insert(field_name, merged_field);
                        },
                        None => {
                            // Add new field
                            merged_fields.insert(field_name, field_type2);
                        }
                    }
                }

                Ok(InferredJsonType::Object {
                    fields: merged_fields,
                    is_homogeneous: false, // Merged objects are heterogeneous
                    total_instances: count1 + count2,
                })
            },

            // Null can merge with any type (becomes optional)
            (InferredJsonType::Null, other) | (other, InferredJsonType::Null) => {
                Ok(other) // Null doesn't change the type, just makes it optional
            },

            // Incompatible types: error
            (t1, t2) => Err(SchemaInferenceError::TypeMergeConflict {
                type1: t1,
                type2: t2
            }),
        }
    }
}

impl InferredJsonType {
    /// Count total elements for capacity calculation
    pub fn count_total_elements(&self) -> usize {
        match self {
            InferredJsonType::Array { total_elements, .. } => *total_elements,
            InferredJsonType::Object { total_instances, .. } => *total_instances,
            InferredJsonType::String { total_instances, .. } => *total_instances,
            _ => 1,
        }
    }

    /// Check if this type is compatible with another for merging
    pub fn is_compatible_with(&self, other: &InferredJsonType) -> bool {
        match (self, other) {
            (InferredJsonType::Null, _) | (_, InferredJsonType::Null) => true,
            (InferredJsonType::String { .. }, InferredJsonType::String { .. }) => true,
            (InferredJsonType::Number, InferredJsonType::Number) => true,
            (InferredJsonType::Boolean, InferredJsonType::Boolean) => true,
            (InferredJsonType::Array { .. }, InferredJsonType::Array { .. }) => true,
            (InferredJsonType::Object { .. }, InferredJsonType::Object { .. }) => true,
            _ => false,
        }
    }
}
```

This architecture provides the foundation for implementing a memory-efficient two-pass JSON schema inference system that can handle arbitrary JSON complexity while maintaining strict memory bounds and type system integrity.
