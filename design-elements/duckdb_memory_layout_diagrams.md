# DuckDB Nested Data Structures: Memory Layout Diagrams

## Overview

This document provides visual representations of how DuckDB internally stores nested data structures, based on analysis of the DuckDB Rust API codebase. These diagrams illustrate the relationship between logical structure (what users see) and physical storage (how DuckDB organizes memory).

## 1. Multi-dimensional Array Encoding

### Example: `LIST[LIST[NUMBER]]` storing `[[1,2,3], [4,5], [6,7,8,9]]`

#### Logical Structure (User View)
```
Row 0: [[1,2,3], [4,5], [6,7,8,9]]
       ├─ Array 0: [1,2,3]
       ├─ Array 1: [4,5]  
       └─ Array 2: [6,7,8,9]
```

#### Physical Memory Layout (DuckDB Internal)

```
┌─────────────────────────────────────────────────────────────────┐
│                    OUTER ListVector (Row Level)                 │
├─────────────────────────────────────────────────────────────────┤
│ entries: FlatVector<duckdb_list_entry>                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Row 0: {offset: 0, length: 3}  // Points to 3 inner arrays │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                   INNER ListVector (Array Level)                │
├─────────────────────────────────────────────────────────────────┤
│ entries: FlatVector<duckdb_list_entry>                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Array 0: {offset: 0, length: 3}   // [1,2,3]              │ │
│ │ Array 1: {offset: 3, length: 2}   // [4,5]                │ │
│ │ Array 2: {offset: 5, length: 4}   // [6,7,8,9]            │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    LEAF FlatVector<f64>                        │
├─────────────────────────────────────────────────────────────────┤
│ data: [1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0, 9.0]         │
│       ↑─────────↑  ↑────↑  ↑──────────────────↑               │
│       Array 0      Array 1      Array 2                       │
└─────────────────────────────────────────────────────────────────┘
```

#### Key Insights:
- **Flat Storage**: All primitive data stored in single contiguous array
- **Offset-Based Access**: Each list entry contains `{offset, length}` pair
- **Cumulative Offsets**: Inner array offsets are cumulative across all elements
- **Two-Level Indirection**: Outer list → Inner list → Data

### Memory Access Pattern
```rust
// Code pattern from duckdb-rs-analysis/crates/duckdb/src/core/vector.rs
pub fn set_entry(&mut self, idx: usize, offset: usize, length: usize) {
    self.entries.as_mut_slice::<duckdb_list_entry>()[idx].offset = offset as u64;
    self.entries.as_mut_slice::<duckdb_list_entry>()[idx].length = length as u64;
}

// Usage for [[1,2,3], [4,5], [6,7,8,9]]:
outer_list.set_entry(0, 0, 3);        // Row 0 has 3 inner arrays
inner_list.set_entry(0, 0, 3);        // Array 0: offset=0, length=3
inner_list.set_entry(1, 3, 2);        // Array 1: offset=3, length=2  
inner_list.set_entry(2, 5, 4);        // Array 2: offset=5, length=4
```

## 2. Arrays of Structs Representation

### Example: `LIST[STRUCT]` storing `[{name: "John", age: 25}, {name: "Jane", age: 30}]`

#### Logical Structure (User View)
```
Row 0: [
  {name: "John", age: 25},
  {name: "Jane", age: 30}
]
```

#### Physical Memory Layout (DuckDB Internal)

```
┌─────────────────────────────────────────────────────────────────┐
│                      ListVector (Row Level)                     │
├─────────────────────────────────────────────────────────────────┤
│ entries: FlatVector<duckdb_list_entry>                         │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ Row 0: {offset: 0, length: 2}  // Points to 2 struct objects│ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                    StructVector (Object Level)                  │
├─────────────────────────────────────────────────────────────────┤
│ Field 0: "name" → FlatVector<String>                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [0]: "John"                                                 │ │
│ │ [1]: "Jane"                                                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Field 1: "age" → FlatVector<i32>                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [0]: 25                                                     │ │
│ │ [1]: 30                                                     │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

#### Vector Hierarchy Access Pattern
```rust
// Code pattern from duckdb_nested_types_reference.md
let mut list_vector = chunk.list_vector(0);
let mut struct_vector = list_vector.struct_child(2);  // 2 struct objects

// Access field vectors by index
let mut name_vector = struct_vector.child(0, 2);      // Field 0: "name"
let mut age_vector = struct_vector.child(1, 2);       // Field 1: "age"

// Insert data
name_vector.insert(0, "John");
name_vector.insert(1, "Jane");
age_vector.as_mut_slice::<i32>()[0] = 25;
age_vector.as_mut_slice::<i32>()[1] = 30;

// Set list entry
list_vector.set_entry(0, 0, 2);  // Row 0 has 2 structs
```

#### Key Insights:
- **Columnar Storage**: Each struct field stored in separate vector
- **Index Alignment**: All field vectors use same indices for same struct
- **Type Specialization**: Each field vector has appropriate primitive type
- **Memory Locality**: Fields stored contiguously within each vector

## 3. Deeply Nested Combinations

### Example: `LIST[STRUCT[scores: LIST[NUMBER], name: STRING]]`
### Data: `[{name: "John", scores: [95, 87]}, {name: "Jane", scores: [92, 88, 91]}]`

#### Complete Vector Tree Structure

```
┌─────────────────────────────────────────────────────────────────┐
│                   ROOT ListVector (Row Level)                   │
│ entries[0]: {offset: 0, length: 2}  // 2 user objects          │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                  StructVector (User Objects)                    │
├─────────────────────────────────────────────────────────────────┤
│ Field 0: "name" → FlatVector<String>                           │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ [0]: "John"                                                 │ │
│ │ [1]: "Jane"                                                 │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ Field 1: "scores" → ListVector (Nested Arrays)                 │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ entries[0]: {offset: 0, length: 2}  // John's 2 scores     │ │
│ │ entries[1]: {offset: 2, length: 3}  // Jane's 3 scores     │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────────┐
│                   LEAF FlatVector<f64> (Scores)                │
│ data: [95.0, 87.0, 92.0, 88.0, 91.0]                         │
│       ↑─────────↑  ↑──────────────────↑                       │
│       John's       Jane's scores                               │
│       scores                                                   │
└─────────────────────────────────────────────────────────────────┘
```

#### Implementation Code Pattern
```rust
// From duckdb_nested_types_reference.md - Complex nested structure processing
let mut root_list = chunk.list_vector(0);
let mut user_struct = root_list.struct_child(2);        // 2 users

// Access name field (Field 0)
let mut name_vector = user_struct.child(0, 2);
name_vector.insert(0, "John");
name_vector.insert(1, "Jane");

// Access scores field (Field 1) - nested list
let mut scores_list = user_struct.list_vector_child(1);
scores_list.set_entry(0, 0, 2);  // John: 2 scores at offset 0
scores_list.set_entry(1, 2, 3);  // Jane: 3 scores at offset 2

// Insert score data
let mut scores_data = scores_list.child(5);  // Total 5 scores
let data_slice = scores_data.as_mut_slice::<f64>();
data_slice[0] = 95.0;  // John's scores
data_slice[1] = 87.0;
data_slice[2] = 92.0;  // Jane's scores
data_slice[3] = 88.0;
data_slice[4] = 91.0;

// Finalize root list
root_list.set_entry(0, 0, 2);  // Row 0 has 2 users
```

## 4. Memory Layout Specifics

### Logical vs Physical Storage Comparison

#### Logical View (JSON-like)
```json
[
  {
    "users": [
      {"name": "John", "scores": [95, 87]},
      {"name": "Jane", "scores": [92, 88, 91]}
    ]
  }
]
```

#### Physical Storage (DuckDB Internal)
```
Memory Region 1: ROOT LIST ENTRIES
┌─────────────────────────────────┐
│ duckdb_list_entry[0]            │
│ {offset: 0, length: 1}          │  // 1 root object
└─────────────────────────────────┘

Memory Region 2: USERS LIST ENTRIES
┌─────────────────────────────────┐
│ duckdb_list_entry[0]            │
│ {offset: 0, length: 2}          │  // 2 users
└─────────────────────────────────┘

Memory Region 3: USER NAMES (Field 0)
┌─────────────────────────────────┐
│ String[0]: "John"               │
│ String[1]: "Jane"               │
└─────────────────────────────────┘

Memory Region 4: SCORES LIST ENTRIES (Field 1)
┌─────────────────────────────────┐
│ duckdb_list_entry[0]            │
│ {offset: 0, length: 2}          │  // John: 2 scores
│ duckdb_list_entry[1]            │
│ {offset: 2, length: 3}          │  // Jane: 3 scores
└─────────────────────────────────┘

Memory Region 5: SCORES DATA
┌─────────────────────────────────┐
│ f64[0]: 95.0  (John)            │
│ f64[1]: 87.0  (John)            │
│ f64[2]: 92.0  (Jane)            │
│ f64[3]: 88.0  (Jane)            │
│ f64[4]: 91.0  (Jane)            │
└─────────────────────────────────┘
```

### Capacity Allocation Strategy

#### Memory Reservation Pattern
```rust
// From duckdb-rs-analysis/crates/duckdb/src/core/vector.rs
fn reserve(&self, capacity: usize) {
    unsafe {
        duckdb_list_vector_reserve(self.entries.ptr, capacity as u64);
    }
}

// Usage pattern for nested structures
let total_scores = calculate_total_scores(users);  // 5 total scores
let mut scores_list = user_struct.list_vector_child(1);
scores_list.reserve(total_scores);  // Reserve space for all scores
```

#### Variable-Length Data Handling
```
BEFORE INSERTION (Empty Vectors):
┌─────────────────────────────────┐
│ ListVector entries: []          │
│ Child capacity: 0               │
└─────────────────────────────────┘

AFTER CAPACITY RESERVATION:
┌─────────────────────────────────┐
│ ListVector entries: [?, ?]      │  // Space for 2 entries
│ Child capacity: 5               │  // Space for 5 elements
└─────────────────────────────────┘

AFTER DATA INSERTION:
┌─────────────────────────────────┐
│ ListVector entries:             │
│   [0]: {offset: 0, length: 2}   │
│   [1]: {offset: 2, length: 3}   │
│ Child data: [95,87,92,88,91]    │
└─────────────────────────────────┘
```

### List Entry Structure Details

#### duckdb_list_entry Memory Layout
```c
// From libduckdb-sys/src/bindgen_bundled_version.rs
struct duckdb_list_entry {
    pub offset: u64,    // 8 bytes - Start position in child vector
    pub length: u64,    // 8 bytes - Number of elements
}
// Total: 16 bytes per list entry
```

#### Offset Calculation Examples
```
Example: [[1,2,3], [4,5], [6,7,8,9]]

Array 0: [1,2,3]
- offset: 0 (starts at beginning)
- length: 3 (has 3 elements)
- occupies child indices: 0, 1, 2

Array 1: [4,5]
- offset: 3 (starts after Array 0)
- length: 2 (has 2 elements)
- occupies child indices: 3, 4

Array 2: [6,7,8,9]
- offset: 5 (starts after Array 1)
- length: 4 (has 4 elements)
- occupies child indices: 5, 6, 7, 8

Child vector data: [1,2,3,4,5,6,7,8,9]
                   ↑───↑ ↑─↑ ↑─────↑
                   Arr0  Arr1  Arr2
```

## 5. Arbitrary Nesting Depth Support

### Recursive Vector Tree Structure

#### Example: 4-Level Nesting
`LIST[LIST[LIST[NUMBER]]]` storing `[[[1,2],[3]],[[4,5,6]]]`

```
Level 0: ROOT ListVector
├─ entries[0]: {offset: 0, length: 2}  // 2 top-level arrays
│
Level 1: OUTER ListVector
├─ entries[0]: {offset: 0, length: 2}  // First: [[1,2],[3]]
├─ entries[1]: {offset: 2, length: 1}  // Second: [[4,5,6]]
│
Level 2: MIDDLE ListVector
├─ entries[0]: {offset: 0, length: 2}  // [1,2]
├─ entries[1]: {offset: 2, length: 1}  // [3]
├─ entries[2]: {offset: 3, length: 3}  // [4,5,6]
│
Level 3: LEAF FlatVector<f64>
└─ data: [1,2,3,4,5,6]
```

#### Memory Access Path
```
To access element [1][0][1] (value: 5):
1. ROOT[1] → offset=2, length=1 → OUTER[2]
2. OUTER[2] → offset=3, length=3 → MIDDLE[3,4,5]
3. MIDDLE[3+1] → LEAF[4] → value: 5
```

### Performance Characteristics

#### Memory Efficiency Benefits
- **Columnar Storage**: Each data type stored contiguously
- **Cache Locality**: Related data stored near each other
- **Minimal Overhead**: Only offset/length pairs for indirection
- **Batch Processing**: Entire vectors processed at once

#### Scalability Considerations
```
Memory Usage for N nested arrays with M total elements:
- List entries: N × 16 bytes (offset + length)
- Element data: M × sizeof(element_type)
- Total overhead: ~16N bytes (very low for large M)

Example: 1000 arrays, 100,000 total numbers
- Entries: 1000 × 16 = 16KB
- Data: 100,000 × 8 = 800KB
- Overhead: 16KB / 816KB = 2% (excellent efficiency)
```

## 6. Implementation Patterns Summary

### Critical Design Principles

1. **Vector Type Matching**: Always use correct vector type for element type
   - `list_vector.child()` → FlatVector (primitives only)
   - `list_vector.struct_child()` → StructVector (objects)
   - `list_vector.list_child()` → ListVector (nested arrays)

2. **Capacity Pre-calculation**: Reserve memory before data insertion
   ```rust
   let total_capacity = calculate_total_elements(data);
   list_vector.reserve(total_capacity);
   ```

3. **Offset Management**: Use cumulative offsets for variable-length arrays
   ```rust
   let mut offset = 0;
   for (idx, array) in arrays.iter().enumerate() {
       list_vector.set_entry(idx, offset, array.len());
       offset += array.len();  // Cumulative for next array
   }
   ```

4. **Recursive Processing**: Handle arbitrary depth without hardcoded limits
   ```rust
   fn process_nested_type(json_type: &JsonValueType) -> LogicalTypeHandle {
       match json_type {
           JsonValueType::Array(element_type) => {
               let element_logical = process_nested_type(element_type);
               LogicalTypeHandle::list(&element_logical)
           }
           JsonValueType::Object(fields) => {
               let struct_fields = fields.iter()
                   .map(|f| (f.name.as_str(), process_nested_type(&f.value_type)))
                   .collect();
               LogicalTypeHandle::struct_type(&struct_fields)
           }
           _ => primitive_type_mapping(json_type),
       }
   }
   ```

### Memory Layout Advantages

- **Zero-Copy Access**: Direct pointer arithmetic to access elements
- **Type Safety**: Each vector enforces appropriate data type
- **Streaming Friendly**: Process data in chunks without loading entire dataset
- **Query Optimization**: DuckDB can optimize operations on columnar data
- **Compression**: Homogeneous data types enable efficient compression

This memory layout enables DuckDB to handle complex JSON structures efficiently while maintaining the performance benefits of columnar storage and type safety.
