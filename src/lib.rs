extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, Inserter, LogicalTypeHandle, LogicalTypeId},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    collections::{HashMap, HashSet, BTreeMap},
    error::Error,
    fs::File,
    io::BufReader,
    path::Path,
    sync::atomic::{AtomicBool, AtomicUsize, Ordering},
};
use struson::reader::{JsonReader, JsonStreamReader};



/// Represents a JSON value type with proper recursive semantics
#[derive(Debug, Clone, PartialEq)]
enum JsonValueType {
    Null,
    Boolean,
    Number,
    String,
    Array(Box<JsonValueType>),      // Array of elements of this type
    Object(Vec<JsonField>),         // Object with named fields
}

/// Represents a field in a JSON object
#[derive(Debug, Clone, PartialEq)]
struct JsonField {
    name: String,
    value_type: JsonValueType,
}



/// Represents the discovered JSON schema with recursive structure
#[derive(Debug, Clone)]
struct JsonSchema {
    root_type: JsonValueType,
    columns: Vec<StructuredColumn>,
}

/// Represents a column with proper structured types (STRUCT/ARRAY)
#[derive(Debug, Clone)]
struct StructuredColumn {
    name: String,
    value_type: JsonValueType,
}

// ============================================================================
// NEW SCHEMA INFERENCE SYSTEM - Two-Pass Architecture
// ============================================================================

/// Represents the complete inferred schema with statistics for two-pass processing
#[derive(Debug, Clone)]
pub struct InferredSchema {
    pub root_type: InferredJsonType,
    pub statistics: SchemaStatistics,
    pub memory_requirements: MemoryRequirements,
}

/// Recursive JSON type representation with capacity information for exact vector allocation
#[derive(Debug, Clone)]
pub enum InferredJsonType {
    Null,
    Boolean,
    Number,
    String {
        max_length: Option<usize>,
        total_instances: usize,
    },
    Array {
        element_type: Box<InferredJsonType>,
        max_length: Option<usize>,
        total_elements: usize,
        array_count: usize,
    },
    Object {
        fields: BTreeMap<String, InferredJsonType>,
        is_homogeneous: bool,
        total_instances: usize,
    },
}

/// Schema statistics for memory planning and validation
#[derive(Debug, Clone)]
pub struct SchemaStatistics {
    pub total_rows: usize,
    pub max_nesting_depth: usize,
    pub unique_field_names: HashSet<String>,
    pub schema_complexity_score: usize,
}

/// Exact memory requirements calculated from schema
#[derive(Debug, Clone)]
pub struct MemoryRequirements {
    pub vector_capacities: HashMap<VectorPath, usize>,
    pub total_memory_estimate: usize,
    pub peak_memory_estimate: usize,
}

/// Identifies specific vectors in the nested structure
#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct VectorPath {
    pub path_components: Vec<PathComponent>,
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub enum PathComponent {
    Root,
    ArrayElement,
    ObjectField(String),
}

/// Configuration for schema inference behavior
#[derive(Debug, Clone)]
pub struct SchemaInferenceConfig {
    pub max_memory_usage: Option<usize>,
    pub max_schema_complexity: usize,
    pub max_unique_fields: usize,
    pub enable_progress_reporting: bool,
    pub enable_debug_output: bool,
}

/// Memory and performance limits
const MAX_SCHEMA_COMPLEXITY: usize = 10_000_000;
const MAX_UNIQUE_FIELD_NAMES: usize = 10_000;
const MAX_ARRAY_ELEMENTS_FOR_INFERENCE: usize = 1000;
const DEFAULT_MEMORY_LIMIT: usize = 100 * 1024 * 1024;

impl Default for SchemaInferenceConfig {
    fn default() -> Self {
        Self {
            max_memory_usage: Some(DEFAULT_MEMORY_LIMIT),
            max_schema_complexity: MAX_SCHEMA_COMPLEXITY,
            max_unique_fields: MAX_UNIQUE_FIELD_NAMES,
            enable_progress_reporting: false,
            enable_debug_output: false,
        }
    }
}

/// Comprehensive error types for schema inference
#[derive(Debug)]
pub enum SchemaInferenceError {
    FileError(std::io::Error),
    JsonError { position: usize, message: String },
    ComplexityExceeded(usize),
    TooManyFields(usize),
    MemoryError(String),
    TypeMergeConflict { type1: String, type2: String },
}

impl std::fmt::Display for SchemaInferenceError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SchemaInferenceError::FileError(e) => write!(f, "File I/O error: {}", e),
            SchemaInferenceError::JsonError { position, message } => {
                write!(f, "JSON parsing error at position {}: {}", position, message)
            },
            SchemaInferenceError::ComplexityExceeded(complexity) => {
                write!(f, "Schema complexity exceeded limit: {} > {}", complexity, MAX_SCHEMA_COMPLEXITY)
            },
            SchemaInferenceError::TooManyFields(count) => {
                write!(f, "Too many unique field names: {} > {}", count, MAX_UNIQUE_FIELD_NAMES)
            },
            SchemaInferenceError::MemoryError(msg) => write!(f, "Memory allocation failed: {}", msg),
            SchemaInferenceError::TypeMergeConflict { type1, type2 } => {
                write!(f, "Type merging conflict: cannot merge {} with {}", type1, type2)
            },
        }
    }
}

impl std::error::Error for SchemaInferenceError {}

impl From<std::io::Error> for SchemaInferenceError {
    fn from(error: std::io::Error) -> Self {
        SchemaInferenceError::FileError(error)
    }
}

impl From<String> for SchemaInferenceError {
    fn from(error: String) -> Self {
        SchemaInferenceError::MemoryError(error)
    }
}

/// Core schema inference engine using pure struson streaming
pub struct SchemaInferrer {
    current_schema: Option<InferredJsonType>,
    nesting_stack: Vec<InferenceContext>,
    statistics: SchemaStatistics,
    memory_tracker: MemoryUsageTracker,
    config: SchemaInferenceConfig,
}

/// Context for tracking inference state at each nesting level
#[derive(Debug)]
struct InferenceContext {
    context_type: ContextType,
    field_schemas: HashMap<String, InferredJsonType>,
    element_count: usize,
    max_array_length: usize,
}

#[derive(Debug)]
enum ContextType {
    RootLevel,
    ArrayElement,
    ObjectField(String),
}

/// Memory usage tracking and bounds enforcement
pub struct MemoryUsageTracker {
    baseline_memory: usize,
    peak_memory: usize,
    current_memory: usize,
    memory_limit: Option<usize>,
    checkpoints: Vec<MemoryCheckpoint>,
}

#[derive(Debug, Clone)]
struct MemoryCheckpoint {
    name: String,
    memory_usage: usize,
    timestamp: std::time::Instant,
}

impl MemoryUsageTracker {
    pub fn new(memory_limit: Option<usize>) -> Self {
        Self {
            baseline_memory: 0,
            peak_memory: 0,
            current_memory: 0,
            memory_limit,
            checkpoints: Vec::new(),
        }
    }

    pub fn start_monitoring(&mut self) {
        self.baseline_memory = self.get_current_memory_usage();
        self.current_memory = self.baseline_memory;
        self.peak_memory = self.baseline_memory;
        self.record_checkpoint("baseline");
    }

    pub fn check_memory_bounds(&mut self) -> Result<(), SchemaInferenceError> {
        self.current_memory = self.get_current_memory_usage();
        self.peak_memory = self.peak_memory.max(self.current_memory);

        if let Some(limit) = self.memory_limit {
            if self.current_memory > limit {
                return Err(SchemaInferenceError::MemoryError(
                    format!("Memory usage {} exceeds limit {}", self.current_memory, limit)
                ));
            }
        }

        Ok(())
    }

    pub fn record_checkpoint(&mut self, name: &str) {
        self.current_memory = self.get_current_memory_usage();
        self.checkpoints.push(MemoryCheckpoint {
            name: name.to_string(),
            memory_usage: self.current_memory,
            timestamp: std::time::Instant::now(),
        });
    }

    fn get_current_memory_usage(&self) -> usize {
        // Platform-specific memory usage detection
        #[cfg(target_os = "linux")]
        {
            self.get_linux_memory_usage()
        }
        #[cfg(not(target_os = "linux"))]
        {
            // Fallback: estimate based on heap allocations
            0 // Placeholder - would need platform-specific implementation
        }
    }

    #[cfg(target_os = "linux")]
    fn get_linux_memory_usage(&self) -> usize {
        use std::fs;
        if let Ok(status) = fs::read_to_string("/proc/self/status") {
            for line in status.lines() {
                if line.starts_with("VmRSS:") {
                    if let Some(kb_str) = line.split_whitespace().nth(1) {
                        if let Ok(kb) = kb_str.parse::<usize>() {
                            return kb * 1024; // Convert KB to bytes
                        }
                    }
                }
            }
        }
        0
    }

    pub fn get_memory_report(&self) -> MemoryReport {
        MemoryReport {
            baseline_memory: self.baseline_memory,
            peak_memory: self.peak_memory,
            current_memory: self.current_memory,
            memory_delta: self.current_memory.saturating_sub(self.baseline_memory),
            checkpoints: self.checkpoints.clone(),
        }
    }
}

#[derive(Debug, Clone)]
pub struct MemoryReport {
    pub baseline_memory: usize,
    pub peak_memory: usize,
    pub current_memory: usize,
    pub memory_delta: usize,
    pub checkpoints: Vec<MemoryCheckpoint>,
}

impl SchemaInferrer {
    pub fn new(config: SchemaInferenceConfig) -> Self {
        Self {
            current_schema: None,
            nesting_stack: Vec::new(),
            statistics: SchemaStatistics {
                total_rows: 0,
                max_nesting_depth: 0,
                unique_field_names: HashSet::new(),
                schema_complexity_score: 0,
            },
            memory_tracker: MemoryUsageTracker::new(config.max_memory_usage),
            config,
        }
    }

    /// Infer schema from streaming JSON reader
    pub fn infer_from_stream(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        self.memory_tracker.start_monitoring();

        let inferred_type = self.infer_value_recursive(json_reader)?;

        // Validate memory bounds
        let complexity = inferred_type.calculate_complexity();
        if complexity > self.config.max_schema_complexity {
            return Err(SchemaInferenceError::ComplexityExceeded(complexity));
        }

        self.statistics.schema_complexity_score = complexity;

        Ok(inferred_type)
    }

    /// Recursive schema inference for arbitrary nesting depth
    fn infer_value_recursive(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        // Check memory bounds periodically
        self.memory_tracker.check_memory_bounds()?;

        // Track nesting depth
        let current_depth = self.nesting_stack.len();
        self.statistics.max_nesting_depth = self.statistics.max_nesting_depth.max(current_depth);

        match json_reader.peek().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            struson::reader::ValueType::Null => {
                json_reader.next_null().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Null)
            },

            struson::reader::ValueType::Boolean => {
                json_reader.next_bool().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Boolean)
            },

            struson::reader::ValueType::Number => {
                json_reader.next_number_as_str().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::Number)
            },

            struson::reader::ValueType::String => {
                let string_value = json_reader.next_string().map_err(|e| SchemaInferenceError::JsonError {
                    position: 0,
                    message: e.to_string()
                })?;
                Ok(InferredJsonType::String {
                    max_length: Some(string_value.len()),
                    total_instances: 1,
                })
            },

            struson::reader::ValueType::Array => {
                self.infer_array_schema(json_reader)
            },

            struson::reader::ValueType::Object => {
                self.infer_object_schema(json_reader)
            },
        }
    }

    /// Infer schema for JSON arrays with element type analysis
    fn infer_array_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut element_type: Option<InferredJsonType> = None;
        let mut element_count = 0;
        let mut total_elements = 0;

        // Push array context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::ArrayElement,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let current_element = self.infer_value_recursive(json_reader)?;
            element_count += 1;
            total_elements += current_element.count_total_elements();

            element_type = Some(match element_type {
                None => current_element,
                Some(existing) => self.merge_types(existing, current_element)?,
            });

            // Check memory bounds during inference
            if element_count > MAX_ARRAY_ELEMENTS_FOR_INFERENCE {
                break; // Stop inferring after reasonable sample
            }
        }

        json_reader.end_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        // Pop array context and update statistics
        if let Some(mut context) = self.nesting_stack.pop() {
            context.element_count = element_count;
            context.max_array_length = element_count;
        }

        Ok(InferredJsonType::Array {
            element_type: Box::new(element_type.unwrap_or(InferredJsonType::Null)),
            max_length: Some(element_count),
            total_elements,
            array_count: 1,
        })
    }

    /// Infer schema for JSON objects with field analysis
    fn infer_object_schema(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_object().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut fields = BTreeMap::new();
        let mut field_count = 0;

        // Push object context
        self.nesting_stack.push(InferenceContext {
            context_type: ContextType::RootLevel,
            field_schemas: HashMap::new(),
            element_count: 0,
            max_array_length: 0,
        });

        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let field_name = json_reader.next_name().map_err(|e| SchemaInferenceError::JsonError {
                position: 0,
                message: e.to_string()
            })?.to_string();
            let field_type = self.infer_value_recursive(json_reader)?;

            fields.insert(field_name.clone(), field_type);
            field_count += 1;

            // Track unique field names for memory bounds
            self.statistics.unique_field_names.insert(field_name);

            // Check field count bounds
            if self.statistics.unique_field_names.len() > self.config.max_unique_fields {
                return Err(SchemaInferenceError::TooManyFields(
                    self.statistics.unique_field_names.len()
                ));
            }
        }

        json_reader.end_object().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        // Pop object context
        self.nesting_stack.pop();

        Ok(InferredJsonType::Object {
            fields,
            is_homogeneous: true, // Will be determined during merging
            total_instances: 1,
        })
    }

    /// Merge two JSON types to handle heterogeneous arrays/objects
    fn merge_types(
        &self,
        type1: InferredJsonType,
        type2: InferredJsonType
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match (type1, type2) {
            // Same types: merge statistics
            (InferredJsonType::String { max_length: len1, total_instances: count1 },
             InferredJsonType::String { max_length: len2, total_instances: count2 }) => {
                Ok(InferredJsonType::String {
                    max_length: Some(len1.unwrap_or(0).max(len2.unwrap_or(0))),
                    total_instances: count1 + count2,
                })
            },

            // Array types: merge element types and statistics
            (InferredJsonType::Array { element_type: elem1, total_elements: total1, array_count: count1, .. },
             InferredJsonType::Array { element_type: elem2, total_elements: total2, array_count: count2, .. }) => {
                let merged_element = self.merge_types(*elem1, *elem2)?;
                Ok(InferredJsonType::Array {
                    element_type: Box::new(merged_element),
                    max_length: None, // Will be calculated during capacity planning
                    total_elements: total1 + total2,
                    array_count: count1 + count2,
                })
            },

            // Object types: merge field schemas
            (InferredJsonType::Object { fields: fields1, total_instances: count1, .. },
             InferredJsonType::Object { fields: fields2, total_instances: count2, .. }) => {
                let mut merged_fields = fields1;

                for (field_name, field_type2) in fields2 {
                    match merged_fields.get(&field_name) {
                        Some(field_type1) => {
                            // Merge existing field
                            let merged_field = self.merge_types(field_type1.clone(), field_type2)?;
                            merged_fields.insert(field_name, merged_field);
                        },
                        None => {
                            // Add new field
                            merged_fields.insert(field_name, field_type2);
                        }
                    }
                }

                Ok(InferredJsonType::Object {
                    fields: merged_fields,
                    is_homogeneous: false, // Merged objects are heterogeneous
                    total_instances: count1 + count2,
                })
            },

            // Null can merge with any type (becomes optional)
            (InferredJsonType::Null, other) | (other, InferredJsonType::Null) => {
                Ok(other) // Null doesn't change the type, just makes it optional
            },

            // Incompatible types: error
            (t1, t2) => Err(SchemaInferenceError::TypeMergeConflict {
                type1: format!("{:?}", t1),
                type2: format!("{:?}", t2)
            }),
        }
    }
}

impl InferredJsonType {
    /// Convert InferredJsonType to legacy JsonValueType for compatibility
    pub fn to_legacy_json_type(&self) -> JsonValueType {
        match self {
            InferredJsonType::Null => JsonValueType::Null,
            InferredJsonType::Boolean => JsonValueType::Boolean,
            InferredJsonType::Number => JsonValueType::Number,
            InferredJsonType::String { .. } => JsonValueType::String,
            InferredJsonType::Array { element_type, .. } => {
                JsonValueType::Array(Box::new(element_type.to_legacy_json_type()))
            },
            InferredJsonType::Object { fields, .. } => {
                let legacy_fields = fields.iter()
                    .map(|(name, field_type)| JsonField {
                        name: name.clone(),
                        value_type: field_type.to_legacy_json_type(),
                    })
                    .collect();
                JsonValueType::Object(legacy_fields)
            },
        }
    }

    /// Create InferredJsonType from legacy JsonValueType
    pub fn from_legacy_json_type(legacy_type: &JsonValueType) -> Self {
        match legacy_type {
            JsonValueType::Null => InferredJsonType::Null,
            JsonValueType::Boolean => InferredJsonType::Boolean,
            JsonValueType::Number => InferredJsonType::Number,
            JsonValueType::String => InferredJsonType::String {
                max_length: None,
                total_instances: 1,
            },
            JsonValueType::Array(element_type) => InferredJsonType::Array {
                element_type: Box::new(Self::from_legacy_json_type(element_type)),
                max_length: None,
                total_elements: 0,
                array_count: 1,
            },
            JsonValueType::Object(fields) => {
                let inferred_fields = fields.iter()
                    .map(|field| (field.name.clone(), Self::from_legacy_json_type(&field.value_type)))
                    .collect();
                InferredJsonType::Object {
                    fields: inferred_fields,
                    is_homogeneous: true,
                    total_instances: 1,
                }
            },
        }
    }

    /// Get the memory footprint of this schema representation
    pub fn memory_footprint(&self) -> usize {
        match self {
            InferredJsonType::Null |
            InferredJsonType::Boolean |
            InferredJsonType::Number => std::mem::size_of::<Self>(),

            InferredJsonType::String { .. } => {
                std::mem::size_of::<Self>() + std::mem::size_of::<usize>() * 2
            },

            InferredJsonType::Array { element_type, .. } => {
                std::mem::size_of::<Self>() + element_type.memory_footprint()
            },

            InferredJsonType::Object { fields, .. } => {
                let fields_size = fields.iter()
                    .map(|(name, field_type)| name.len() + field_type.memory_footprint())
                    .sum::<usize>();
                std::mem::size_of::<Self>() + fields_size
            },
        }
    }

    /// Optimize schema representation by removing redundant information
    pub fn optimize(&mut self) {
        match self {
            InferredJsonType::Array { element_type, .. } => {
                element_type.optimize();
            },
            InferredJsonType::Object { fields, .. } => {
                for (_, field_type) in fields.iter_mut() {
                    field_type.optimize();
                }
                // Remove fields with zero instances (if any)
                fields.retain(|_, field_type| field_type.count_total_elements() > 0);
            },
            _ => {}, // Primitives don't need optimization
        }
    }

    /// Calculate memory complexity score for this type
    pub fn calculate_complexity(&self) -> usize {
        match self {
            InferredJsonType::Null |
            InferredJsonType::Boolean |
            InferredJsonType::Number => 1,

            InferredJsonType::String { max_length, .. } => {
                // String complexity based on maximum length
                max_length.unwrap_or(100).min(1000) // Cap at reasonable limit
            },

            InferredJsonType::Array { element_type, array_count, .. } => {
                // Array complexity: base cost + element complexity * array count
                10 + element_type.calculate_complexity() * (*array_count).min(1000)
            },

            InferredJsonType::Object { fields, total_instances, .. } => {
                // Object complexity: field count + sum of field complexities
                let field_complexity: usize = fields.values()
                    .map(|field_type| field_type.calculate_complexity())
                    .sum();
                fields.len() * 5 + field_complexity * (*total_instances).min(1000)
            },
        }
    }

    /// Count total elements for capacity calculation
    pub fn count_total_elements(&self) -> usize {
        match self {
            InferredJsonType::Array { total_elements, .. } => *total_elements,
            InferredJsonType::Object { total_instances, .. } => *total_instances,
            InferredJsonType::String { total_instances, .. } => *total_instances,
            _ => 1,
        }
    }

    /// Check if this type is compatible with another for merging
    pub fn is_compatible_with(&self, other: &InferredJsonType) -> bool {
        match (self, other) {
            (InferredJsonType::Null, _) | (_, InferredJsonType::Null) => true,
            (InferredJsonType::String { .. }, InferredJsonType::String { .. }) => true,
            (InferredJsonType::Number, InferredJsonType::Number) => true,
            (InferredJsonType::Boolean, InferredJsonType::Boolean) => true,
            (InferredJsonType::Array { .. }, InferredJsonType::Array { .. }) => true,
            (InferredJsonType::Object { .. }, InferredJsonType::Object { .. }) => true,
            _ => false,
        }
    }

    /// Validate schema consistency and detect potential issues
    pub fn validate(&self) -> Result<(), String> {
        match self {
            InferredJsonType::Array { element_type, total_elements, array_count, .. } => {
                // Validate array consistency
                if *total_elements == 0 && *array_count > 0 {
                    return Err("Array has count > 0 but total_elements = 0".to_string());
                }
                element_type.validate()?;
            },
            InferredJsonType::Object { fields, total_instances, .. } => {
                // Validate object consistency
                if fields.is_empty() && *total_instances > 0 {
                    return Err("Object has instances > 0 but no fields".to_string());
                }
                for (field_name, field_type) in fields {
                    if field_name.is_empty() {
                        return Err("Object field has empty name".to_string());
                    }
                    field_type.validate()?;
                }
            },
            InferredJsonType::String { total_instances, .. } => {
                if *total_instances == 0 {
                    return Err("String type has zero instances".to_string());
                }
            },
            _ => {}, // Other types are always valid
        }
        Ok(())
    }

    /// Create a compact string representation for debugging
    pub fn to_compact_string(&self) -> String {
        match self {
            InferredJsonType::Null => "null".to_string(),
            InferredJsonType::Boolean => "bool".to_string(),
            InferredJsonType::Number => "num".to_string(),
            InferredJsonType::String { max_length, total_instances } => {
                format!("str(max:{:?},count:{})", max_length, total_instances)
            },
            InferredJsonType::Array { element_type, total_elements, array_count, .. } => {
                format!("arr[{}](elems:{},count:{})",
                    element_type.to_compact_string(), total_elements, array_count)
            },
            InferredJsonType::Object { fields, total_instances, is_homogeneous } => {
                let field_strs: Vec<String> = fields.iter()
                    .map(|(name, field_type)| format!("{}:{}", name, field_type.to_compact_string()))
                    .collect();
                format!("obj{{{}}}(count:{},homo:{})",
                    field_strs.join(","), total_instances, is_homogeneous)
            },
        }
    }
}

impl InferredSchema {
    /// Create a new schema with validation
    pub fn new(root_type: InferredJsonType) -> Result<Self, String> {
        root_type.validate()?;

        let complexity = root_type.calculate_complexity();
        let memory_footprint = root_type.memory_footprint();

        let statistics = SchemaStatistics {
            total_rows: 1, // Will be updated during inference
            max_nesting_depth: Self::calculate_nesting_depth(&root_type),
            unique_field_names: Self::collect_field_names(&root_type),
            schema_complexity_score: complexity,
        };

        // Calculate memory requirements
        let calculator = VectorCapacityCalculator::new(root_type.clone(), 1);
        let vector_capacities = calculator.calculate_all_capacities()
            .map_err(|e| format!("Failed to calculate capacities: {}", e))?;

        let memory_requirements = MemoryRequirements {
            vector_capacities: vector_capacities.capacities,
            total_memory_estimate: vector_capacities.total_memory,
            peak_memory_estimate: vector_capacities.total_memory * 2,
        };

        Ok(InferredSchema {
            root_type,
            statistics,
            memory_requirements,
        })
    }

    /// Calculate maximum nesting depth in the schema
    fn calculate_nesting_depth(json_type: &InferredJsonType) -> usize {
        match json_type {
            InferredJsonType::Array { element_type, .. } => {
                1 + Self::calculate_nesting_depth(element_type)
            },
            InferredJsonType::Object { fields, .. } => {
                1 + fields.values()
                    .map(|field_type| Self::calculate_nesting_depth(field_type))
                    .max()
                    .unwrap_or(0)
            },
            _ => 0,
        }
    }

    /// Collect all unique field names in the schema
    fn collect_field_names(json_type: &InferredJsonType) -> HashSet<String> {
        let mut field_names = HashSet::new();
        Self::collect_field_names_recursive(json_type, &mut field_names);
        field_names
    }

    fn collect_field_names_recursive(json_type: &InferredJsonType, field_names: &mut HashSet<String>) {
        match json_type {
            InferredJsonType::Array { element_type, .. } => {
                Self::collect_field_names_recursive(element_type, field_names);
            },
            InferredJsonType::Object { fields, .. } => {
                for (field_name, field_type) in fields {
                    field_names.insert(field_name.clone());
                    Self::collect_field_names_recursive(field_type, field_names);
                }
            },
            _ => {},
        }
    }

    /// Get a summary of the schema for debugging
    pub fn summary(&self) -> String {
        format!(
            "Schema Summary:\n  Root: {}\n  Complexity: {}\n  Memory Footprint: {} bytes\n  Max Depth: {}\n  Unique Fields: {}\n  Total Memory: {} bytes",
            self.root_type.to_compact_string(),
            self.statistics.schema_complexity_score,
            self.root_type.memory_footprint(),
            self.statistics.max_nesting_depth,
            self.statistics.unique_field_names.len(),
            self.memory_requirements.total_memory_estimate
        )
    }
}

impl VectorPath {
    /// Append a path component to create a new path
    pub fn append(&self, component: PathComponent) -> VectorPath {
        let mut new_components = self.path_components.clone();
        new_components.push(component);
        VectorPath { path_components: new_components }
    }

    /// Convert path to human-readable string for debugging
    pub fn to_string(&self) -> String {
        self.path_components.iter()
            .map(|component| match component {
                PathComponent::Root => "root".to_string(),
                PathComponent::ArrayElement => "[]".to_string(),
                PathComponent::ObjectField(name) => format!(".{}", name),
            })
            .collect::<Vec<_>>()
            .join("")
    }
}

/// Calculates exact DuckDB vector capacities from inferred schema
pub struct VectorCapacityCalculator {
    schema: InferredJsonType,
    row_count: usize,
}

/// Container for all calculated vector capacities
#[derive(Debug, Clone)]
pub struct VectorCapacities {
    pub capacities: HashMap<VectorPath, usize>,
    pub total_memory: usize,
}

/// Error types for capacity calculation
#[derive(Debug)]
pub enum CapacityError {
    CapacityOverflow { path: String },
    InvalidCapacity { capacity: usize, type_name: String },
}

impl std::fmt::Display for CapacityError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            CapacityError::CapacityOverflow { path } => {
                write!(f, "Capacity calculation overflow for path {}", path)
            },
            CapacityError::InvalidCapacity { capacity, type_name } => {
                write!(f, "Invalid capacity: {} for type {}", capacity, type_name)
            },
        }
    }
}

impl std::error::Error for CapacityError {}

impl From<CapacityError> for SchemaInferenceError {
    fn from(error: CapacityError) -> Self {
        SchemaInferenceError::MemoryError(error.to_string())
    }
}

impl VectorCapacityCalculator {
    pub fn new(schema: InferredJsonType, row_count: usize) -> Self {
        Self { schema, row_count }
    }

    /// Calculate exact capacities for all vectors in the schema
    pub fn calculate_all_capacities(&self) -> Result<VectorCapacities, CapacityError> {
        let mut capacities = HashMap::new();
        let root_path = VectorPath { path_components: vec![PathComponent::Root] };

        self.calculate_type_capacity(
            &self.schema,
            &root_path,
            self.row_count,
            &mut capacities
        )?;

        Ok(VectorCapacities {
            capacities: capacities.clone(),
            total_memory: self.calculate_total_memory(&capacities),
        })
    }

    /// Recursive capacity calculation for nested types
    fn calculate_type_capacity(
        &self,
        json_type: &InferredJsonType,
        current_path: &VectorPath,
        instance_count: usize,
        capacities: &mut HashMap<VectorPath, usize>
    ) -> Result<(), CapacityError> {
        match json_type {
            InferredJsonType::Array { element_type, total_elements, .. } => {
                // List vector needs capacity for all elements across all arrays
                let array_capacity = total_elements.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), array_capacity);

                // Calculate capacity for child elements
                let child_path = current_path.append(PathComponent::ArrayElement);
                self.calculate_type_capacity(
                    element_type,
                    &child_path,
                    array_capacity,
                    capacities
                )?;
            },

            InferredJsonType::Object { fields, total_instances, .. } => {
                // Struct vector capacity is number of struct instances
                let struct_capacity = total_instances.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), struct_capacity);

                // Calculate capacity for each field
                for (field_name, field_type) in fields {
                    let field_path = current_path.append(PathComponent::ObjectField(field_name.clone()));
                    self.calculate_type_capacity(
                        field_type,
                        &field_path,
                        struct_capacity,
                        capacities
                    )?;
                }
            },

            InferredJsonType::String { total_instances, .. } => {
                // String vector capacity is number of instances
                let string_capacity = total_instances.checked_mul(instance_count)
                    .ok_or_else(|| CapacityError::CapacityOverflow {
                        path: current_path.to_string()
                    })?;
                capacities.insert(current_path.clone(), string_capacity);
            },

            InferredJsonType::Number => {
                // Number vector capacity is number of instances
                capacities.insert(current_path.clone(), instance_count);
            },

            InferredJsonType::Boolean => {
                // Boolean vector capacity is number of instances
                capacities.insert(current_path.clone(), instance_count);
            },

            InferredJsonType::Null => {
                // Null values don't need capacity
                capacities.insert(current_path.clone(), 0);
            },
        }

        Ok(())
    }

    /// Calculate total memory requirement from all capacities
    fn calculate_total_memory(&self, capacities: &HashMap<VectorPath, usize>) -> usize {
        capacities.values().sum::<usize>() * 8 // Assume 8 bytes per element average
    }
}

/// Public API function for schema inference from file
pub fn infer_schema_from_file(
    file_path: &str,
    config: Option<SchemaInferenceConfig>
) -> Result<InferredSchema, SchemaInferenceError> {
    let config = config.unwrap_or_default();
    let mut inferrer = SchemaInferrer::new(config);

    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(SchemaInferenceError::FileError(
            std::io::Error::new(std::io::ErrorKind::NotFound, format!("File does not exist: {}", file_path))
        ));
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Infer schema from the JSON file
    let root_type = inferrer.infer_from_stream(&mut json_reader)?;

    // Calculate exact memory requirements using the capacity calculator
    let capacity_calculator = VectorCapacityCalculator::new(root_type.clone(), 1); // Assume 1 row for schema inference
    let vector_capacities = capacity_calculator.calculate_all_capacities()?;

    let memory_requirements = MemoryRequirements {
        vector_capacities: vector_capacities.capacities,
        total_memory_estimate: vector_capacities.total_memory,
        peak_memory_estimate: vector_capacities.total_memory * 2, // Conservative estimate
    };

    Ok(InferredSchema {
        root_type,
        statistics: inferrer.statistics,
        memory_requirements,
    })
}

/// Test function for vector capacity calculation
#[cfg(test)]
pub fn test_vector_capacity_calculation() -> Result<(), Box<dyn std::error::Error>> {
    // Create a simple test schema: Array of Objects with String and Number fields
    let test_schema = InferredJsonType::Array {
        element_type: Box::new(InferredJsonType::Object {
            fields: {
                let mut fields = BTreeMap::new();
                fields.insert("name".to_string(), InferredJsonType::String {
                    max_length: Some(50),
                    total_instances: 3,
                });
                fields.insert("age".to_string(), InferredJsonType::Number);
                fields
            },
            is_homogeneous: true,
            total_instances: 3,
        }),
        max_length: Some(3),
        total_elements: 3,
        array_count: 1,
    };

    let calculator = VectorCapacityCalculator::new(test_schema, 1);
    let capacities = calculator.calculate_all_capacities()?;

    eprintln!("Test Vector Capacities:");
    for (path, capacity) in &capacities.capacities {
        eprintln!("  {}: {}", path.to_string(), capacity);
    }
    eprintln!("Total Memory: {} bytes", capacities.total_memory);

    Ok(())
}

/// Test function for schema representation system
#[cfg(test)]
pub fn test_schema_representation() -> Result<(), Box<dyn std::error::Error>> {
    // Create a complex test schema
    let test_schema = InferredJsonType::Object {
        fields: {
            let mut fields = BTreeMap::new();
            fields.insert("users".to_string(), InferredJsonType::Array {
                element_type: Box::new(InferredJsonType::Object {
                    fields: {
                        let mut user_fields = BTreeMap::new();
                        user_fields.insert("name".to_string(), InferredJsonType::String {
                            max_length: Some(50),
                            total_instances: 5,
                        });
                        user_fields.insert("scores".to_string(), InferredJsonType::Array {
                            element_type: Box::new(InferredJsonType::Number),
                            max_length: Some(3),
                            total_elements: 15,
                            array_count: 5,
                        });
                        user_fields
                    },
                    is_homogeneous: true,
                    total_instances: 5,
                }),
                max_length: Some(5),
                total_elements: 5,
                array_count: 1,
            });
            fields.insert("metadata".to_string(), InferredJsonType::Object {
                fields: {
                    let mut meta_fields = BTreeMap::new();
                    meta_fields.insert("version".to_string(), InferredJsonType::String {
                        max_length: Some(10),
                        total_instances: 1,
                    });
                    meta_fields
                },
                is_homogeneous: true,
                total_instances: 1,
            });
            fields
        },
        is_homogeneous: true,
        total_instances: 1,
    };

    // Test schema creation and validation
    let schema = InferredSchema::new(test_schema)?;

    eprintln!("Schema Representation Test:");
    eprintln!("{}", schema.summary());
    eprintln!("Compact representation: {}", schema.root_type.to_compact_string());

    // Test legacy conversion
    let legacy_type = schema.root_type.to_legacy_json_type();
    let converted_back = InferredJsonType::from_legacy_json_type(&legacy_type);
    eprintln!("Legacy conversion test: {}", converted_back.to_compact_string());

    Ok(())
}

// ============================================================================
// PASS 1: SCHEMA INFERENCE PASS - Two-Pass System Implementation
// ============================================================================

/// Complete Pass 1 implementation for schema inference and capacity calculation
pub struct SchemaInferencePass {
    inferrer: SchemaInferrer,
    config: SchemaInferenceConfig,
    progress_tracker: ProgressTracker,
}

/// Progress tracking for large file processing
pub struct ProgressTracker {
    file_size: u64,
    bytes_processed: u64,
    start_time: std::time::Instant,
    last_report_time: std::time::Instant,
    enable_reporting: bool,
}

impl ProgressTracker {
    pub fn new(file_size: u64, enable_reporting: bool) -> Self {
        let now = std::time::Instant::now();
        Self {
            file_size,
            bytes_processed: 0,
            start_time: now,
            last_report_time: now,
            enable_reporting,
        }
    }

    pub fn update_progress(&mut self, bytes_processed: u64) {
        self.bytes_processed = bytes_processed;

        if self.enable_reporting {
            let now = std::time::Instant::now();
            if now.duration_since(self.last_report_time).as_secs() >= 5 {
                self.report_progress();
                self.last_report_time = now;
            }
        }
    }

    pub fn report_progress(&self) {
        let percentage = if self.file_size > 0 {
            (self.bytes_processed as f64 / self.file_size as f64) * 100.0
        } else {
            0.0
        };

        let elapsed = self.start_time.elapsed();
        let rate = if elapsed.as_secs() > 0 {
            self.bytes_processed as f64 / elapsed.as_secs() as f64
        } else {
            0.0
        };

        eprintln!("Schema Inference Progress: {:.1}% ({} / {} bytes) - {:.1} bytes/sec",
                 percentage, self.bytes_processed, self.file_size, rate);
    }

    pub fn finish(&self) {
        if self.enable_reporting {
            let elapsed = self.start_time.elapsed();
            eprintln!("Schema Inference Complete: {} bytes processed in {:.2}s",
                     self.bytes_processed, elapsed.as_secs_f64());
        }
    }
}

impl SchemaInferencePass {
    pub fn new(config: SchemaInferenceConfig) -> Self {
        let inferrer = SchemaInferrer::new(config.clone());
        Self {
            inferrer,
            config: config.clone(),
            progress_tracker: ProgressTracker::new(0, config.enable_progress_reporting),
        }
    }

    /// Infer schema from entire JSON file with progress tracking and memory monitoring
    pub fn infer_schema_from_file(&mut self, file_path: &str) -> Result<InferredSchema, SchemaInferenceError> {
        // Check if file exists and get size
        let file_metadata = std::fs::metadata(file_path)
            .map_err(|e| SchemaInferenceError::FileError(e))?;
        let file_size = file_metadata.len();

        self.progress_tracker = ProgressTracker::new(file_size, self.config.enable_progress_reporting);

        if self.config.enable_debug_output {
            eprintln!("Starting schema inference for file: {} ({} bytes)", file_path, file_size);
        }

        // Open file and create JSON reader
        let file = File::open(file_path)?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(buf_reader);

        // Start memory monitoring
        self.inferrer.memory_tracker.start_monitoring();
        self.inferrer.memory_tracker.record_checkpoint("start_inference");

        // Infer schema from the JSON file
        let root_type = self.infer_complete_structure(&mut json_reader)?;

        // Final memory check
        self.inferrer.memory_tracker.check_memory_bounds()?;
        self.inferrer.memory_tracker.record_checkpoint("inference_complete");

        // Create complete schema with validation
        let schema = InferredSchema::new(root_type)?;

        self.progress_tracker.finish();

        if self.config.enable_debug_output {
            eprintln!("Schema inference completed successfully");
            eprintln!("{}", schema.summary());
        }

        Ok(schema)
    }

    /// Infer complete JSON structure handling root-level arrays and objects
    fn infer_complete_structure(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        match json_reader.peek().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            struson::reader::ValueType::Array => {
                // Root-level array: process all elements to get complete schema
                self.infer_root_array_complete(json_reader)
            },
            struson::reader::ValueType::Object => {
                // Root-level object: process single object
                self.inferrer.infer_value_recursive(json_reader)
            },
            _ => {
                // Single primitive value at root
                self.inferrer.infer_value_recursive(json_reader)
            }
        }
    }

    /// Infer schema from root-level array by processing ALL elements (not just first)
    fn infer_root_array_complete(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>
    ) -> Result<InferredJsonType, SchemaInferenceError> {
        json_reader.begin_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        let mut merged_element_type: Option<InferredJsonType> = None;
        let mut element_count: usize = 0;
        let mut total_elements: usize = 0;

        // Process ALL elements in the array for complete schema
        while json_reader.has_next().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })? {
            let current_element = self.inferrer.infer_value_recursive(json_reader)?;
            element_count += 1;
            total_elements += current_element.count_total_elements();

            merged_element_type = Some(match merged_element_type {
                None => current_element,
                Some(existing) => self.inferrer.merge_types(existing, current_element)?,
            });

            // Update progress periodically
            if element_count % 1000 == 0 {
                self.progress_tracker.update_progress((element_count * 100) as u64); // Rough estimate
                self.inferrer.memory_tracker.check_memory_bounds()?;
            }

            // Check if we should stop for memory reasons
            if element_count > MAX_ARRAY_ELEMENTS_FOR_INFERENCE {
                if self.config.enable_debug_output {
                    eprintln!("Stopping array inference after {} elements (memory limit)", element_count);
                }
                break;
            }
        }

        json_reader.end_array().map_err(|e| SchemaInferenceError::JsonError {
            position: 0,
            message: e.to_string()
        })?;

        Ok(InferredJsonType::Array {
            element_type: Box::new(merged_element_type.unwrap_or(InferredJsonType::Null)),
            max_length: Some(element_count),
            total_elements,
            array_count: 1,
        })
    }

    /// Calculate vector capacities from inferred schema
    pub fn calculate_vector_capacities(&self, schema: &InferredSchema) -> VectorCapacities {
        let calculator = VectorCapacityCalculator::new(schema.root_type.clone(), schema.statistics.total_rows);
        calculator.calculate_all_capacities()
            .unwrap_or_else(|_| VectorCapacities {
                capacities: HashMap::new(),
                total_memory: 0,
            })
    }

    /// Get memory usage report from the inference process
    pub fn get_memory_report(&self) -> String {
        let report = self.inferrer.memory_tracker.get_memory_report();
        format!(
            "Memory Usage Report:\n  Baseline: {} bytes\n  Peak: {} bytes\n  Delta: {} bytes\n  Checkpoints: {}",
            report.baseline_memory,
            report.peak_memory,
            report.memory_delta,
            report.checkpoints.len()
        )
    }
}

/// Test function for Pass 1 - Schema Inference Pass
#[cfg(test)]
pub fn test_pass1_schema_inference() -> Result<(), Box<dyn std::error::Error>> {
    // Create a test JSON file content (in memory simulation)
    let test_json = r#"[
        {"name": "Alice", "age": 30, "scores": [95, 87, 92]},
        {"name": "Bob", "age": 25, "scores": [88, 91]},
        {"name": "Charlie", "age": 35, "scores": [93, 89, 94, 96]}
    ]"#;

    // Write test data to a temporary file
    use std::io::Write;
    let mut temp_file = std::fs::File::create("test_schema_inference.json")?;
    temp_file.write_all(test_json.as_bytes())?;
    temp_file.sync_all()?;
    drop(temp_file);

    // Test Pass 1 schema inference
    let config = SchemaInferenceConfig {
        enable_debug_output: true,
        enable_progress_reporting: true,
        ..Default::default()
    };

    let mut pass1 = SchemaInferencePass::new(config);
    let schema = pass1.infer_schema_from_file("test_schema_inference.json")?;

    eprintln!("Pass 1 Test Results:");
    eprintln!("{}", schema.summary());
    eprintln!("Root type: {}", schema.root_type.to_compact_string());

    // Test vector capacity calculation
    let capacities = pass1.calculate_vector_capacities(&schema);
    eprintln!("Vector Capacities:");
    for (path, capacity) in &capacities.capacities {
        eprintln!("  {}: {}", path.to_string(), capacity);
    }

    // Test memory report
    eprintln!("{}", pass1.get_memory_report());

    // Clean up
    std::fs::remove_file("test_schema_inference.json").ok();

    Ok(())
}

// ============================================================================
// PASS 2: STREAMING DATA LOADER - Two-Pass System Implementation
// ============================================================================

/// Complete Pass 2 implementation for streaming data loading with pre-allocated vectors
pub struct StreamingDataLoader {
    schema: InferredSchema,
    vector_capacities: VectorCapacities,
    offset_tracker: CumulativeOffsetTracker,
    config: DataLoaderConfig,
}

/// Configuration for data loading behavior
#[derive(Debug, Clone)]
pub struct DataLoaderConfig {
    pub enable_debug_output: bool,
    pub enable_progress_reporting: bool,
    pub batch_size: usize,
    pub validate_offsets: bool,
}

impl Default for DataLoaderConfig {
    fn default() -> Self {
        Self {
            enable_debug_output: false,
            enable_progress_reporting: false,
            batch_size: 2048, // DuckDB standard vector size
            validate_offsets: true,
        }
    }
}

/// Tracks cumulative offsets across all vectors for proper memory layout
pub struct CumulativeOffsetTracker {
    current_offsets: HashMap<VectorPath, usize>,
    capacity_limits: HashMap<VectorPath, usize>,
    debug_mode: bool,
    allocation_history: Vec<OffsetAllocation>,
}

#[derive(Debug, Clone)]
struct OffsetAllocation {
    vector_path: VectorPath,
    start_offset: usize,
    length: usize,
    row_index: usize,
}

/// Range of offsets allocated for a specific data insertion
#[derive(Debug, Clone)]
pub struct OffsetRange {
    pub start_offset: usize,
    pub length: usize,
    pub end_offset: usize,
}

/// Error types for data loading
#[derive(Debug)]
pub enum DataLoaderError {
    SchemaError(String),
    OffsetError(String),
    CapacityExceeded { vector_path: String, required: usize, available: usize },
    JsonParsingError(String),
    VectorError(String),
}

impl std::fmt::Display for DataLoaderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DataLoaderError::SchemaError(msg) => write!(f, "Schema error: {}", msg),
            DataLoaderError::OffsetError(msg) => write!(f, "Offset error: {}", msg),
            DataLoaderError::CapacityExceeded { vector_path, required, available } => {
                write!(f, "Capacity exceeded for {}: required {}, available {}", vector_path, required, available)
            },
            DataLoaderError::JsonParsingError(msg) => write!(f, "JSON parsing error: {}", msg),
            DataLoaderError::VectorError(msg) => write!(f, "Vector error: {}", msg),
        }
    }
}

impl std::error::Error for DataLoaderError {}

impl CumulativeOffsetTracker {
    pub fn new(vector_capacities: &VectorCapacities, debug_mode: bool) -> Self {
        let mut current_offsets = HashMap::new();
        let mut capacity_limits = HashMap::new();

        // Initialize all vector paths with zero offset and their capacity limits
        for (vector_path, capacity) in &vector_capacities.capacities {
            current_offsets.insert(vector_path.clone(), 0);
            capacity_limits.insert(vector_path.clone(), *capacity);
        }

        Self {
            current_offsets,
            capacity_limits,
            debug_mode,
            allocation_history: Vec::new(),
        }
    }

    /// Allocate a range of offsets for a specific vector path
    pub fn allocate_range(&mut self, vector_path: &VectorPath, length: usize, row_index: usize) -> Result<OffsetRange, DataLoaderError> {
        let current_offset = self.current_offsets.get(vector_path).copied().unwrap_or(0);
        let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);

        // Check capacity bounds
        if current_offset + length > capacity_limit {
            return Err(DataLoaderError::CapacityExceeded {
                vector_path: vector_path.to_string(),
                required: current_offset + length,
                available: capacity_limit,
            });
        }

        let range = OffsetRange {
            start_offset: current_offset,
            length,
            end_offset: current_offset + length,
        };

        // Update current offset
        self.current_offsets.insert(vector_path.clone(), current_offset + length);

        // Record allocation for debugging
        if self.debug_mode {
            self.allocation_history.push(OffsetAllocation {
                vector_path: vector_path.clone(),
                start_offset: current_offset,
                length,
                row_index,
            });
        }

        Ok(range)
    }

    /// Validate that all capacity usage is within bounds
    pub fn validate_capacity_usage(&self) -> Result<(), DataLoaderError> {
        for (vector_path, current_offset) in &self.current_offsets {
            let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);
            if *current_offset > capacity_limit {
                return Err(DataLoaderError::CapacityExceeded {
                    vector_path: vector_path.to_string(),
                    required: *current_offset,
                    available: capacity_limit,
                });
            }
        }
        Ok(())
    }

    /// Get debug report of offset allocations
    pub fn get_debug_report(&self) -> String {
        let mut report = String::new();
        report.push_str("Cumulative Offset Tracker Report:\n");

        for (vector_path, current_offset) in &self.current_offsets {
            let capacity_limit = self.capacity_limits.get(vector_path).copied().unwrap_or(0);
            let usage_percent = if capacity_limit > 0 {
                (*current_offset as f64 / capacity_limit as f64) * 100.0
            } else {
                0.0
            };

            report.push_str(&format!(
                "  {}: {}/{} ({:.1}%)\n",
                vector_path.to_string(),
                current_offset,
                capacity_limit,
                usage_percent
            ));
        }

        if self.debug_mode && !self.allocation_history.is_empty() {
            report.push_str("\nAllocation History:\n");
            for allocation in &self.allocation_history {
                report.push_str(&format!(
                    "  Row {}: {} [{}..{}] (len={})\n",
                    allocation.row_index,
                    allocation.vector_path.to_string(),
                    allocation.start_offset,
                    allocation.start_offset + allocation.length,
                    allocation.length
                ));
            }
        }

        report
    }
}

impl StreamingDataLoader {
    pub fn new(schema: InferredSchema, vector_capacities: VectorCapacities, config: DataLoaderConfig) -> Self {
        let offset_tracker = CumulativeOffsetTracker::new(&vector_capacities, config.enable_debug_output);

        Self {
            schema,
            vector_capacities,
            offset_tracker,
            config,
        }
    }

    /// Load JSON data directly into pre-allocated DuckDB vectors using known schema
    pub fn load_data_from_file(
        &mut self,
        file_path: &str,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        if self.config.enable_debug_output {
            eprintln!("Pass 2: Starting data loading for file: {}", file_path);
            eprintln!("Schema: {}", self.schema.summary());
        }

        // Open file and create JSON reader
        let file = File::open(file_path)
            .map_err(|e| DataLoaderError::JsonParsingError(format!("Failed to open file: {}", e)))?;
        let buf_reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(buf_reader);

        // Load data based on root schema type
        let rows_processed = match &self.schema.root_type {
            InferredJsonType::Array { .. } => {
                self.load_root_array_data(&mut json_reader, output, column_index)?
            },
            InferredJsonType::Object { .. } => {
                self.load_single_object_data(&mut json_reader, output, column_index)?
            },
            _ => {
                self.load_single_primitive_data(&mut json_reader, output, column_index)?
            }
        };

        // Validate offset usage
        self.offset_tracker.validate_capacity_usage()?;

        if self.config.enable_debug_output {
            eprintln!("Pass 2: Completed data loading. Rows processed: {}", rows_processed);
            eprintln!("{}", self.offset_tracker.get_debug_report());
        }

        Ok(rows_processed)
    }

    /// Load data from root-level JSON array
    fn load_root_array_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        let mut row_count = 0;

        // Get the output vector for this column
        let mut list_vector = output.list_vector(column_index);

        // Pre-allocate vector capacity based on schema
        let root_path = VectorPath { path_components: vec![PathComponent::Root] };
        if let Some(_total_capacity) = self.vector_capacities.capacities.get(&root_path) {
            // Note: reserve method is private, capacity will be managed by DuckDB
        }

        // Process each array element as a row
        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            self.load_array_element_as_row(json_reader, &mut list_vector, row_count)?;
            row_count += 1;

            // Progress reporting
            if self.config.enable_progress_reporting && row_count % 1000 == 0 {
                eprintln!("Pass 2: Processed {} rows", row_count);
            }
        }

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // Set final vector length
        list_vector.set_len(row_count);

        Ok(row_count)
    }

    /// Load a single array element as a row in the output
    fn load_array_element_as_row(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        list_vector: &mut duckdb::core::ListVector,
        row_index: usize,
    ) -> Result<(), DataLoaderError> {
        // Get the element type from the array schema
        let element_type = if let InferredJsonType::Array { element_type, .. } = &self.schema.root_type {
            element_type.clone()
        } else {
            return Err(DataLoaderError::SchemaError("Root type is not an array".to_string()));
        };

        match element_type.as_ref() {
            InferredJsonType::Array { .. } => {
                // Multi-dimensional array: use recursive loading
                self.load_multidimensional_array_element(json_reader, list_vector, row_index, &element_type)?;
            },
            InferredJsonType::Object { .. } => {
                // Array of objects: load as struct
                self.load_object_element(json_reader, list_vector, row_index, &element_type)?;
            },
            _ => {
                // Array of primitives: load directly
                self.load_primitive_array_element(json_reader, list_vector, row_index, &element_type)?;
            }
        }

        Ok(())
    }

    /// Load multi-dimensional array element using proper offset coordination
    fn load_multidimensional_array_element(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        list_vector: &mut duckdb::core::ListVector,
        row_index: usize,
        element_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // Parse the array structure first
        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        let mut sub_arrays = Vec::new();
        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            let sub_array = self.parse_nested_array_structure(json_reader, element_type)?;
            sub_arrays.push(sub_array);
        }

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // Calculate offset range for this row
        let array_path = VectorPath {
            path_components: vec![PathComponent::Root, PathComponent::ArrayElement]
        };
        let offset_range = self.offset_tracker.allocate_range(&array_path, sub_arrays.len(), row_index)?;

        // Set list entry for this row
        list_vector.set_entry(row_index, offset_range.start_offset, sub_arrays.len());

        // Insert the sub-arrays into the child vector
        let mut child_list_vector = list_vector.list_child();
        self.insert_nested_arrays_with_offsets(&mut child_list_vector, &sub_arrays, &offset_range, element_type)?;

        if self.config.enable_debug_output {
            eprintln!("Pass 2: Row {} - inserted {} sub-arrays at offset {}",
                     row_index, sub_arrays.len(), offset_range.start_offset);
        }

        Ok(())
    }

    /// Load data from single JSON object (non-array root)
    fn load_single_object_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        // For single object, create one row
        let mut struct_vector = output.struct_vector(column_index);
        let root_type = self.schema.root_type.clone();
        self.load_object_into_struct(json_reader, &mut struct_vector, 0, &root_type)?;
        Ok(1)
    }

    /// Load data from single primitive value (non-array, non-object root)
    fn load_single_primitive_data(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        output: &DataChunkHandle,
        column_index: usize,
    ) -> Result<usize, DataLoaderError> {
        // For single primitive, create one row
        let mut flat_vector = output.flat_vector(column_index);
        let root_type = self.schema.root_type.clone();
        self.load_primitive_into_vector(json_reader, &mut flat_vector, 0, &root_type)?;
        Ok(1)
    }

    /// Load object element into struct vector
    fn load_object_element(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        list_vector: &mut duckdb::core::ListVector,
        row_index: usize,
        element_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // Get struct child vector
        let mut struct_child_vector = list_vector.struct_child(row_index);

        // Load object data into struct
        self.load_object_into_struct(json_reader, &mut struct_child_vector, row_index, element_type)?;

        // Set list entry for this row (single object per row)
        list_vector.set_entry(row_index, row_index, 1);

        Ok(())
    }

    /// Load primitive array element
    fn load_primitive_array_element(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        list_vector: &mut duckdb::core::ListVector,
        row_index: usize,
        element_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // Parse array of primitives
        json_reader.begin_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        let mut elements = Vec::new();
        while json_reader.has_next()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            let element = self.parse_primitive_value(json_reader, element_type)?;
            elements.push(element);
        }

        json_reader.end_array()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

        // Calculate offset for this row's elements
        let element_path = VectorPath {
            path_components: vec![PathComponent::Root, PathComponent::ArrayElement]
        };
        let offset_range = self.offset_tracker.allocate_range(&element_path, elements.len(), row_index)?;

        // Set list entry
        list_vector.set_entry(row_index, offset_range.start_offset, elements.len());

        // Insert elements into child vector
        self.insert_primitives_into_child_vector(list_vector, &elements, &offset_range, element_type)?;

        Ok(())
    }

    /// Parse nested array structure recursively
    fn parse_nested_array_structure(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        element_type: &InferredJsonType,
    ) -> Result<Vec<ParsedValue>, DataLoaderError> {
        if let InferredJsonType::Array { element_type: inner_type, .. } = element_type {
            json_reader.begin_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            let mut sub_elements = Vec::new();
            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                match inner_type.as_ref() {
                    InferredJsonType::Array { .. } => {
                        // Recursively parse deeper arrays
                        let deeper_array = self.parse_nested_array_structure(json_reader, inner_type)?;
                        sub_elements.push(ParsedValue::Array(deeper_array));
                    },
                    _ => {
                        // Parse primitive or object
                        let value = self.parse_primitive_value(json_reader, inner_type)?;
                        sub_elements.push(value);
                    }
                }
            }

            json_reader.end_array()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            Ok(sub_elements)
        } else {
            Err(DataLoaderError::SchemaError("Expected array type for nested parsing".to_string()))
        }
    }
}

/// Parsed value types for intermediate storage during loading
#[derive(Debug, Clone)]
enum ParsedValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<ParsedValue>),
    Object(HashMap<String, ParsedValue>),
}

impl StreamingDataLoader {
    /// Load object data into struct vector
    fn load_object_into_struct(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        struct_vector: &mut duckdb::core::StructVector,
        row_index: usize,
        object_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        if let InferredJsonType::Object { fields, .. } = object_type {
            json_reader.begin_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            // Parse all fields from JSON
            let mut parsed_fields = HashMap::new();
            while json_reader.has_next()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                let field_name = json_reader.next_name()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                    .to_string();

                if let Some(field_type) = fields.get(&field_name) {
                    let field_value = self.parse_value_by_type(json_reader, field_type)?;
                    parsed_fields.insert(field_name, field_value);
                } else {
                    // Skip unknown field
                    self.skip_json_value(json_reader)?;
                }
            }

            json_reader.end_object()
                .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

            // Insert parsed fields into struct vector
            for (field_index, (field_name, field_type)) in fields.iter().enumerate() {
                let mut field_vector = struct_vector.child(field_index, 1); // Capacity of 1 for single row

                if let Some(parsed_value) = parsed_fields.get(field_name) {
                    self.insert_parsed_value_into_vector(&mut field_vector, row_index, parsed_value, field_type)?;
                } else {
                    // Field not present in JSON - set as null
                    field_vector.set_null(row_index);
                }
            }
        } else {
            return Err(DataLoaderError::SchemaError("Expected object type".to_string()));
        }

        Ok(())
    }

    /// Load primitive value into flat vector
    fn load_primitive_into_vector(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        flat_vector: &mut duckdb::core::FlatVector,
        row_index: usize,
        value_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        let parsed_value = self.parse_value_by_type(json_reader, value_type)?;
        self.insert_parsed_value_into_vector(flat_vector, row_index, &parsed_value, value_type)?;
        Ok(())
    }

    /// Parse JSON value according to expected type
    fn parse_value_by_type(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        expected_type: &InferredJsonType,
    ) -> Result<ParsedValue, DataLoaderError> {
        match json_reader.peek()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            struson::reader::ValueType::Null => {
                json_reader.next_null()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::Null)
            },

            struson::reader::ValueType::Boolean => {
                let value = json_reader.next_bool()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::Boolean(value))
            },

            struson::reader::ValueType::Number => {
                let value_str = json_reader.next_number_as_str()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                let value = value_str.parse::<f64>()
                    .map_err(|e| DataLoaderError::JsonParsingError(format!("Invalid number: {}", e)))?;
                Ok(ParsedValue::Number(value))
            },

            struson::reader::ValueType::String => {
                let value = json_reader.next_string()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                Ok(ParsedValue::String(value))
            },

            struson::reader::ValueType::Array => {
                let array_elements = self.parse_nested_array_structure(json_reader, expected_type)?;
                Ok(ParsedValue::Array(array_elements))
            },

            struson::reader::ValueType::Object => {
                if let InferredJsonType::Object { fields, .. } = expected_type {
                    json_reader.begin_object()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

                    let mut object_fields = HashMap::new();
                    while json_reader.has_next()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

                        let field_name = json_reader.next_name()
                            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?
                            .to_string();

                        if let Some(field_type) = fields.get(&field_name) {
                            let field_value = self.parse_value_by_type(json_reader, field_type)?;
                            object_fields.insert(field_name, field_value);
                        } else {
                            self.skip_json_value(json_reader)?;
                        }
                    }

                    json_reader.end_object()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;

                    Ok(ParsedValue::Object(object_fields))
                } else {
                    Err(DataLoaderError::SchemaError("Expected object type for JSON object".to_string()))
                }
            },
        }
    }

    /// Parse primitive value (simplified version)
    fn parse_primitive_value(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
        expected_type: &InferredJsonType,
    ) -> Result<ParsedValue, DataLoaderError> {
        self.parse_value_by_type(json_reader, expected_type)
    }

    /// Skip a JSON value without parsing it
    fn skip_json_value(
        &mut self,
        json_reader: &mut JsonStreamReader<BufReader<File>>,
    ) -> Result<(), DataLoaderError> {
        match json_reader.peek()
            .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {

            struson::reader::ValueType::Null => {
                json_reader.next_null()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Boolean => {
                json_reader.next_bool()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Number => {
                json_reader.next_number_as_str()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::String => {
                json_reader.next_string()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Array => {
                json_reader.begin_array()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {
                    self.skip_json_value(json_reader)?;
                }
                json_reader.end_array()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
            struson::reader::ValueType::Object => {
                json_reader.begin_object()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                while json_reader.has_next()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))? {
                    json_reader.next_name()
                        .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
                    self.skip_json_value(json_reader)?;
                }
                json_reader.end_object()
                    .map_err(|e| DataLoaderError::JsonParsingError(e.to_string()))?;
            },
        }
        Ok(())
    }

    /// Insert parsed value into any vector type
    fn insert_parsed_value_into_vector(
        &mut self,
        _vector: &mut dyn std::any::Any,
        _row_index: usize,
        parsed_value: &ParsedValue,
        _expected_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // This is a simplified implementation - in practice would need proper type dispatch
        match parsed_value {
            ParsedValue::Null => {
                // Set null - would need proper vector type handling
                Ok(())
            },
            ParsedValue::Number(_n) => {
                // Insert number - would need proper vector type handling
                Ok(())
            },
            ParsedValue::String(_s) => {
                // Insert string - would need proper vector type handling
                Ok(())
            },
            ParsedValue::Boolean(_b) => {
                // Insert boolean - would need proper vector type handling
                Ok(())
            },
            _ => {
                // Complex types would need recursive handling
                Ok(())
            }
        }
    }

    /// Insert primitives into child vector
    fn insert_primitives_into_child_vector(
        &mut self,
        list_vector: &mut duckdb::core::ListVector,
        elements: &[ParsedValue],
        offset_range: &OffsetRange,
        element_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        match element_type {
            InferredJsonType::Number => {
                let mut child_vector = list_vector.child(offset_range.length);

                // First pass: set null values
                for (i, element) in elements.iter().enumerate() {
                    if !matches!(element, ParsedValue::Number(_)) {
                        child_vector.set_null(offset_range.start_offset + i);
                    }
                }

                // Second pass: set number values
                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(offset_range.length);
                for (i, element) in elements.iter().enumerate() {
                    if let ParsedValue::Number(n) = element {
                        data_slice[i] = *n;
                    }
                }
            },
            InferredJsonType::String { .. } => {
                let mut child_vector = list_vector.child(offset_range.length);

                for (i, element) in elements.iter().enumerate() {
                    if let ParsedValue::String(s) = element {
                        child_vector.insert(offset_range.start_offset + i, s.as_str());
                    } else {
                        child_vector.set_null(offset_range.start_offset + i);
                    }
                }
            },
            _ => {
                // Other types would need specific handling
                return Err(DataLoaderError::VectorError("Unsupported primitive type".to_string()));
            }
        }

        Ok(())
    }

    /// Insert nested arrays with proper offset coordination
    fn insert_nested_arrays_with_offsets(
        &mut self,
        child_list_vector: &mut duckdb::core::ListVector,
        sub_arrays: &[Vec<ParsedValue>],
        offset_range: &OffsetRange,
        element_type: &InferredJsonType,
    ) -> Result<(), DataLoaderError> {
        // This would implement the recursive array insertion logic
        // Similar to the existing insert_multidimensional_array_recursive but with proper offset tracking

        if let InferredJsonType::Array { element_type: inner_type, .. } = element_type {
            match inner_type.as_ref() {
                InferredJsonType::Number => {
                    // Calculate total capacity needed
                    let total_elements: usize = sub_arrays.iter().map(|arr| arr.len()).sum();
                    let mut child_vector = child_list_vector.child(total_elements);
                    let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_elements);

                    let mut current_offset = 0;
                    for (array_idx, sub_array) in sub_arrays.iter().enumerate() {
                        // Set list entry for this sub-array
                        child_list_vector.set_entry(
                            offset_range.start_offset + array_idx,
                            current_offset,
                            sub_array.len()
                        );

                        // Insert data
                        for element in sub_array {
                            if let ParsedValue::Number(n) = element {
                                data_slice[current_offset] = *n;
                            }
                            current_offset += 1;
                        }
                    }
                },
                _ => {
                    return Err(DataLoaderError::VectorError("Unsupported nested array type".to_string()));
                }
            }
        }

        Ok(())
    }
}

/// Test function for Pass 2 - Streaming Data Loader
#[cfg(test)]
pub fn test_pass2_streaming_data_loader() -> Result<(), Box<dyn std::error::Error>> {
    // Create a test JSON file content
    let test_json = r#"[
        {"name": "Alice", "age": 30},
        {"name": "Bob", "age": 25},
        {"name": "Charlie", "age": 35}
    ]"#;

    // Write test data to a temporary file
    use std::io::Write;
    let mut temp_file = std::fs::File::create("test_pass2_data_loader.json")?;
    temp_file.write_all(test_json.as_bytes())?;
    temp_file.sync_all()?;
    drop(temp_file);

    // First, run Pass 1 to get schema
    let config = SchemaInferenceConfig {
        enable_debug_output: true,
        ..Default::default()
    };

    let mut pass1 = SchemaInferencePass::new(config);
    let schema = pass1.infer_schema_from_file("test_pass2_data_loader.json")?;

    eprintln!("Pass 2 Test - Inferred Schema:");
    eprintln!("{}", schema.summary());

    // Calculate vector capacities
    let capacities = pass1.calculate_vector_capacities(&schema);

    // Test Pass 2 data loading
    let loader_config = DataLoaderConfig {
        enable_debug_output: true,
        ..Default::default()
    };

    let _pass2 = StreamingDataLoader::new(schema, capacities, loader_config);

    // Create a mock output chunk for testing
    // Note: This would normally be provided by DuckDB
    eprintln!("Pass 2 Test: StreamingDataLoader created successfully");
    eprintln!("Pass 2 Test: Would load data from file in real implementation");

    // Clean up
    std::fs::remove_file("test_pass2_data_loader.json").ok();

    Ok(())
}

// ============================================================================
// EXISTING SCHEMA DISCOVERY SYSTEM (Legacy - will be replaced by two-pass)
// ============================================================================

#[repr(C)]
struct JsonReaderBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonReaderInitData {
    current_element: AtomicUsize,
    finished: AtomicBool,
    projected_columns: Vec<usize>, // Columns requested by the query
}

struct JsonReaderVTab;

// Helper function to convert InferredJsonType to legacy StructuredColumn format
fn generate_columns_from_inferred_schema(inferred_type: &InferredJsonType) -> Vec<StructuredColumn> {
    match inferred_type {
        InferredJsonType::Array { element_type, .. } => {
            // For root arrays, flatten elements into rows with a single 'value' column
            vec![StructuredColumn {
                name: "value".to_string(),
                value_type: element_type.to_legacy_json_type(),
            }]
        },
        InferredJsonType::Object { fields, .. } => {
            // For objects, create columns for each field
            fields.iter().map(|(field_name, field_type)| {
                StructuredColumn {
                    name: field_name.clone(),
                    value_type: field_type.to_legacy_json_type(),
                }
            }).collect()
        },
        _ => {
            // For primitive types at root, create a single 'value' column
            vec![StructuredColumn {
                name: "value".to_string(),
                value_type: inferred_type.to_legacy_json_type(),
            }]
        }
    }
}

// Helper function to discover JSON schema with proper recursive analysis
fn discover_json_schema(
    file_path: &str,
    projected_columns: Option<&[usize]>
) -> Result<JsonSchema, Box<dyn std::error::Error>> {
    eprintln!("!!! DISCOVER_JSON_SCHEMA CALLED WITH FILE: {}", file_path);
    // Check if file exists
    if !Path::new(file_path).exists() {
        return Err(format!("File does not exist: {}", file_path).into());
    }

    // Open file and create JSON reader
    let file = File::open(file_path)?;
    let buf_reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(buf_reader);

    // Analyze the root JSON structure recursively
    let root_type = analyze_json_value(&mut json_reader)?;

    // Generate structured columns based on the discovered structure
    let columns = if let Some(projected) = projected_columns {
        // Query-driven: only generate columns for projected fields
        eprintln!("DEBUG SCHEMA: Using projected columns mode");
        generate_projected_columns(&root_type, projected)?
    } else {
        // Discovery mode: generate all possible columns
        eprintln!("DEBUG SCHEMA: Using discovery mode, calling generate_all_columns");
        eprintln!("DEBUG SCHEMA: Root type: {:?}", root_type);
        let cols = generate_all_columns(&root_type)?;
        eprintln!("DEBUG SCHEMA: Generated columns: {:?}", cols);
        cols
    };

    if columns.is_empty() {
        // Handle empty objects by creating a single column like DuckDB's built-in JSON reader
        // DuckDB requires table functions to return at least one column
        return Ok(JsonSchema {
            root_type,
            columns: vec![StructuredColumn {
                name: "json".to_string(),
                value_type: JsonValueType::String, // Will contain the JSON string representation
            }],
        });
    }

    Ok(JsonSchema {
        root_type,
        columns,
    })
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Recursively analyze JSON structure to build proper type representation
fn analyze_json_value(
    json_reader: &mut JsonStreamReader<BufReader<File>>
) -> Result<JsonValueType, Box<dyn std::error::Error>> {
    match json_reader.peek()? {
        struson::reader::ValueType::Null => {
            json_reader.next_null()?;
            Ok(JsonValueType::Null)
        }
        struson::reader::ValueType::Boolean => {
            json_reader.next_bool()?;
            Ok(JsonValueType::Boolean)
        }
        struson::reader::ValueType::Number => {
            json_reader.next_number_as_str()?;
            Ok(JsonValueType::Number)
        }
        struson::reader::ValueType::String => {
            json_reader.next_string()?;
            Ok(JsonValueType::String)
        }
        struson::reader::ValueType::Array => {
            json_reader.begin_array()?;

            // Analyze first element to determine array element type
            let element_type = if json_reader.has_next()? {
                let first_element_type = analyze_json_value(json_reader)?;

                // Skip remaining elements for now (we could analyze more for union types)
                while json_reader.has_next()? {
                    json_reader.skip_value()?;
                }

                first_element_type
            } else {
                // Empty array - assume string elements
                JsonValueType::String
            };

            json_reader.end_array()?;
            Ok(JsonValueType::Array(Box::new(element_type)))
        }
        struson::reader::ValueType::Object => {
            json_reader.begin_object()?;
            let mut fields = Vec::new();

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                let field_type = analyze_json_value(json_reader)?;

                fields.push(JsonField {
                    name: field_name,
                    value_type: field_type,
                });
            }

            json_reader.end_object()?;
            Ok(JsonValueType::Object(fields))
        }
    }
}

// Generate columns for projected fields only
fn generate_projected_columns(
    _root_type: &JsonValueType,
    _projected: &[usize]
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    // TODO: Implement query-driven column generation
    Err("Query-driven column generation not yet implemented".into())
}

// Generate structured columns from JSON structure (preserving hierarchy)
fn generate_all_columns(
    root_type: &JsonValueType
) -> Result<Vec<StructuredColumn>, Box<dyn std::error::Error>> {
    match root_type {
        JsonValueType::Object(fields) => {
            // Root object: each field becomes a top-level column
            let mut columns = Vec::new();
            for field in fields {
                columns.push(StructuredColumn {
                    name: field.name.clone(),
                    value_type: field.value_type.clone(),
                });
            }
            Ok(columns)
        }
        JsonValueType::Array(element_type) => {
            // Root array: flatten array elements into rows
            eprintln!("DEBUG SCHEMA: Root array detected, flattening elements into rows");
            match element_type.as_ref() {
                JsonValueType::Object(fields) => {
                    // Array of objects: each object field becomes a column
                    eprintln!("DEBUG SCHEMA: Array of objects with {} fields", fields.len());
                    let mut columns = Vec::new();
                    for field in fields {
                        eprintln!("DEBUG SCHEMA: Adding column '{}' with type {:?}", field.name, field.value_type);
                        columns.push(StructuredColumn {
                            name: field.name.clone(),
                            value_type: field.value_type.clone(),
                        });
                    }
                    eprintln!("DEBUG SCHEMA: Generated {} flattened columns", columns.len());
                    Ok(columns)
                }
                _ => {
                    // Array of primitives: single column with element type
                    eprintln!("DEBUG SCHEMA: Array of primitives, creating single 'value' column");
                    Ok(vec![StructuredColumn {
                        name: "value".to_string(),
                        value_type: element_type.as_ref().clone(),
                    }])
                }
            }
        }
        _ => {
            // Root primitive: single column
            Ok(vec![StructuredColumn {
                name: "value".to_string(),
                value_type: root_type.clone(),
            }])
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data
//
// Convert JsonValueType to DuckDB LogicalTypeHandle
fn json_type_to_duckdb_type(json_type: &JsonValueType) -> Result<LogicalTypeHandle, Box<dyn std::error::Error>> {
    match json_type {
        JsonValueType::Boolean => Ok(LogicalTypeHandle::from(LogicalTypeId::Boolean)),
        JsonValueType::Number => Ok(LogicalTypeHandle::from(LogicalTypeId::Double)), // Use proper Double type for numbers
        JsonValueType::String => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Null => Ok(LogicalTypeHandle::from(LogicalTypeId::Varchar)),
        JsonValueType::Array(element_type) => {
            // Create proper LIST type with correct element type
            let element_logical_type = json_type_to_duckdb_type(element_type)?;
            Ok(LogicalTypeHandle::list(&element_logical_type))
        }
        JsonValueType::Object(fields) => {
            // Create proper STRUCT type for nested objects
            let mut struct_fields = Vec::new();

            for field in fields {
                let field_type = json_type_to_duckdb_type(&field.value_type)?;
                struct_fields.push((field.name.as_str(), field_type));
            }

            Ok(LogicalTypeHandle::struct_type(&struct_fields))
        }
    }
}

// Apply projection pushdown to only include requested columns
fn apply_projection_pushdown(
    full_schema: &JsonSchema,
    projected_column_names: &[String],
) -> JsonSchema {
    eprintln!("DEBUG PROJECTION: Applying pushdown for columns: {:?}", projected_column_names);

    // If no specific projection, return full schema
    if projected_column_names.is_empty() {
        eprintln!("DEBUG PROJECTION: No projection specified, using full schema");
        return full_schema.clone();
    }

    // Filter columns to only include projected ones
    let mut projected_columns = Vec::new();

    for column_name in projected_column_names {
        if let Some(column) = full_schema.columns.iter().find(|col| &col.name == column_name) {
            eprintln!("DEBUG PROJECTION: Including column: {}", column_name);
            projected_columns.push(column.clone());
        } else {
            eprintln!("DEBUG PROJECTION: Warning - requested column '{}' not found in schema", column_name);
        }
    }

    JsonSchema {
        root_type: full_schema.root_type.clone(),
        columns: projected_columns,
    }
}

// Read JSON file using streaming parser and write directly to DuckDB vectors for memory efficiency
fn read_json_streaming_to_vectors(
    file_path: &str,
    schema: &JsonSchema,
    output: &DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use std::fs::File;
    use struson::reader::{JsonReader, JsonStreamReader};

    eprintln!("DEBUG STREAMING: Opening file for streaming JSON parsing: {}", file_path);

    // Validate file exists and is readable
    if !std::path::Path::new(file_path).exists() {
        return Err(format!("JSON file not found: {}", file_path).into());
    }

    // Open file for streaming with error handling
    let file = match File::open(file_path) {
        Ok(f) => f,
        Err(e) => return Err(format!("Failed to open JSON file '{}': {}", file_path, e).into()),
    };

    let mut json_reader = JsonStreamReader::new(file);
    let mut row_count = 0;

    // Start reading the JSON structure with comprehensive error handling
    match json_reader.peek() {
        Ok(value_type) => match value_type {
            struson::reader::ValueType::Object => {
                eprintln!("DEBUG STREAMING: Processing single JSON object");
                match read_object_streaming_to_vectors(&mut json_reader, schema, output, row_count) {
                    Ok(()) => row_count += 1,
                    Err(e) => return Err(format!("Error parsing JSON object: {}", e).into()),
                }
            }
            struson::reader::ValueType::Array => {
                eprintln!("DEBUG STREAMING: Processing JSON array");
                match json_reader.begin_array() {
                    Ok(_) => {
                        while json_reader.has_next().unwrap_or(false) {
                            if row_count > 10000 {
                                eprintln!("DEBUG STREAMING: Warning - processing large array with {} elements", row_count + 1);
                            }

                            // For root-level arrays, each element becomes a row with flattened columns
                            match read_array_element_as_row(&mut json_reader, schema, output, row_count) {
                                Ok(()) => row_count += 1,
                                Err(e) => {
                                    eprintln!("DEBUG STREAMING: Warning - skipping malformed array element at position {}: {}", row_count + 1, e);
                                    // Try to skip the malformed element
                                    if let Err(skip_err) = json_reader.skip_value() {
                                        return Err(format!("Failed to skip malformed element: {}", skip_err).into());
                                    }
                                }
                            }
                        }

                        if let Err(e) = json_reader.end_array() {
                            return Err(format!("Error closing JSON array: {}", e).into());
                        }
                    }
                    Err(e) => return Err(format!("Error starting JSON array: {}", e).into()),
                }
            }
            _ => {
                return Err(format!("Unsupported JSON root type: expected object or array, found {:?}", value_type).into());
            }
        },
        Err(e) => {
            return Err(format!("Failed to read JSON file '{}': {} (file may be empty or malformed)", file_path, e).into());
        }
    }

    eprintln!("DEBUG STREAMING: Successfully parsed {} rows using streaming", row_count);

    if row_count == 0 {
        eprintln!("DEBUG STREAMING: Warning - no valid data rows found in JSON file");
    }

    Ok(row_count)
}

// Read a single array element and write it as a flattened row
fn read_array_element_as_row(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Object => {
            // Object element: read fields and map to flattened columns
            json_reader.begin_object()?;

            while json_reader.has_next()? {
                let field_name = json_reader.next_name()?.to_string();
                eprintln!("DEBUG ARRAY_FLATTEN: Processing field: {}", field_name);

                // Find the column index for this field in the flattened schema
                if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
                    eprintln!("DEBUG ARRAY_FLATTEN: Found column {} for field {}", col_idx, field_name);

                    // Read the field value directly into the appropriate column
                    match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Successfully inserted field '{}' at column {}", field_name, col_idx);
                        }
                        Err(e) => {
                            eprintln!("DEBUG ARRAY_FLATTEN: Error inserting field '{}': {}", field_name, e);
                            // Set null value for this field
                            set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                            // Try to skip the problematic value
                            if let Err(skip_err) = json_reader.skip_value() {
                                return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                            }
                        }
                    }
                } else {
                    // Field not in schema - skip it
                    eprintln!("DEBUG ARRAY_FLATTEN: Skipping unknown field: {}", field_name);
                    json_reader.skip_value()?;
                }
            }

            json_reader.end_object()?;
            Ok(())
        }
        _ => {
            // Primitive element: write to single column (assuming schema has one column)
            if schema.columns.len() == 1 {
                read_value_streaming_to_vector(json_reader, output, 0, row_idx, &schema.columns[0].value_type)
            } else {
                Err("Primitive array element but schema has multiple columns".into())
            }
        }
    }
}

// Read a JSON object using streaming parser and write directly to DuckDB vectors
fn read_object_streaming_to_vectors(
    json_reader: &mut JsonStreamReader<File>,
    schema: &JsonSchema,
    output: &DataChunkHandle,
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to vectors at row {}", row_idx);

    // Begin object with error handling
    if let Err(e) = json_reader.begin_object() {
        return Err(format!("Failed to start reading JSON object: {}", e).into());
    }

    // Check if this is an empty object case (single "json" column for empty objects)
    let is_empty_object_case = schema.columns.len() == 1 &&
                               schema.columns[0].name == "json" &&
                               matches!(schema.columns[0].value_type, JsonValueType::String);

    let mut field_count = 0;
    while json_reader.has_next().unwrap_or(false) {
        field_count += 1;

        // Prevent infinite loops on malformed JSON - use configurable limit
        if field_count > MAX_UNIQUE_FIELD_NAMES {
            return Err(format!("JSON object has too many fields (>{}), possible malformed JSON", MAX_UNIQUE_FIELD_NAMES).into());
        }

        let field_name = match json_reader.next_name() {
            Ok(name) => name.to_string(),
            Err(e) => return Err(format!("Failed to read field name at position {}: {}", field_count, e).into()),
        };

        // Find the column index for this field
        if let Some(col_idx) = schema.columns.iter().position(|col| col.name == field_name) {
            // Only parse fields that are in our schema (projection pushdown)
            eprintln!("DEBUG PROJECTION: Parsing required field: {}", field_name);
            match read_value_streaming_to_vector(json_reader, output, col_idx, row_idx, &schema.columns[col_idx].value_type) {
                Ok(()) => {
                    eprintln!("DEBUG STREAMING: Successfully parsed field '{}' at column {}", field_name, col_idx);
                }
                Err(e) => {
                    eprintln!("DEBUG ERROR: Failed to parse field '{}': {}", field_name, e);
                    // Set null value for this field
                    set_vector_null(output, col_idx, row_idx, &schema.columns[col_idx].value_type);
                    // Try to skip the problematic value
                    if let Err(skip_err) = json_reader.skip_value() {
                        return Err(format!("Failed to skip malformed field '{}': {}", field_name, skip_err).into());
                    }
                }
            }
        } else {
            // Skip fields not in schema (projection optimization)
            eprintln!("DEBUG PROJECTION: Skipping unrequested field: {}", field_name);
            if let Err(e) = json_reader.skip_value() {
                return Err(format!("Failed to skip unrequested field '{}': {}", field_name, e).into());
            }
        }
    }

    // End object with error handling
    if let Err(e) = json_reader.end_object() {
        return Err(format!("Failed to close JSON object: {}", e).into());
    }

    // Handle empty object case - set JSON string representation
    if is_empty_object_case && field_count == 0 {
        eprintln!("DEBUG STREAMING: Empty object detected, setting JSON string representation");
        let mut vector = output.flat_vector(0);
        let cstring = std::ffi::CString::new("{}")?;
        vector.insert(row_idx, cstring);
    }

    Ok(())
}

// Unified recursive JSON value insertion system
// This is the core function that handles ALL JSON nesting patterns uniformly
fn insert_json_value_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            set_vector_null_unified(&vector_context, row_idx, expected_type);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Boolean(b))?;
                }
                _ => return Err("Invalid vector context for primitive boolean".into()),
            }
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::Number(number_value))?;
                }
                _ => return Err("Invalid vector context for primitive number".into()),
            }
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            eprintln!("DEBUG STRING: Extracted string value: '{}'", s);
            match &vector_context {
                VectorContext::DataChunk(output, col_idx) => {
                    eprintln!("DEBUG STRING: Inserting string '{}' at col_idx={}, row_idx={}", s, col_idx, row_idx);
                    insert_primitive_to_data_chunk(output, *col_idx, row_idx, &TempValue::String(s))?;
                }
                _ => return Err("Invalid vector context for primitive string".into()),
            }
            Ok(())
        }
        ValueType::Array => {
            insert_array_recursive(json_reader, vector_context, row_idx, expected_type)
        }
        ValueType::Object => {
            insert_object_recursive(json_reader, vector_context, row_idx, expected_type)
        }
    }
}

// Unified vector context that can represent any DuckDB vector type
enum VectorContext<'a> {
    DataChunk(&'a DataChunkHandle, usize), // (output, col_idx)
    FlatVector(&'a mut duckdb::core::FlatVector),
    ListVector(&'a mut duckdb::core::ListVector),
    StructVector(&'a mut duckdb::core::StructVector),
}

// Read a JSON value using streaming parser and write directly to DuckDB vector
// This is now a wrapper around the unified recursive system
fn read_value_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    let vector_context = VectorContext::DataChunk(output, col_idx);
    insert_json_value_recursive(json_reader, vector_context, row_idx, expected_type)
}

// Unified null value insertion
fn set_vector_null_unified(
    vector_context: &VectorContext,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match vector_context {
        VectorContext::DataChunk(output, col_idx) => {
            set_vector_null(output, *col_idx, row_idx, value_type);
        }
        VectorContext::FlatVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on FlatVector reference");
        }
        VectorContext::ListVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on ListVector reference");
        }
        VectorContext::StructVector(_vector) => {
            // Cannot set null on immutable reference - this needs redesign
            eprintln!("DEBUG: Cannot set null on StructVector reference");
        }
    }
}

// Unified primitive value insertion - redesigned to avoid borrowing issues
fn insert_primitive_to_data_chunk(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    temp_value: &TempValue,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG CHUNK: Getting flat_vector for col_idx={}", col_idx);
    let mut vector = output.flat_vector(col_idx);
    eprintln!("DEBUG CHUNK: Got flat_vector, inserting temp_value");
    let result = insert_temp_value_to_vector(&mut vector, row_idx, temp_value, &JsonValueType::String);
    eprintln!("DEBUG CHUNK: insert_temp_value_to_vector result: {:?}", result);
    result
}


// Unified array insertion
fn insert_array_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Array(element_type) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_array_streaming_to_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
            VectorContext::ListVector(_list_vector) => {
                // Handle array within existing list context - this is multi-dimensional arrays
                // For now, return error - will implement this next
                Err("Multi-dimensional arrays not yet implemented".into())
            }
            _ => {
                Err("Invalid vector context for array insertion".into())
            }
        }
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Unified object insertion
fn insert_object_recursive(
    json_reader: &mut JsonStreamReader<File>,
    vector_context: VectorContext,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(fields) = expected_type {
        match vector_context {
            VectorContext::DataChunk(output, col_idx) => {
                read_object_streaming_to_struct_vector(json_reader, output, col_idx, row_idx, expected_type)
            }
            VectorContext::StructVector(_struct_vector) => {
                // Handle object within existing struct context - this is nested objects
                // For now, return error - will implement this next
                Err("Nested objects in struct context not yet implemented".into())
            }
            _ => {
                Err("Invalid vector context for object insertion".into())
            }
        }
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Read array from streaming parser and write directly to DuckDB list vector
fn read_array_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    if let JsonValueType::Array(element_type) = expected_type {
        let mut list_vector = output.list_vector(col_idx);
        json_reader.begin_array()?;

        match element_type.as_ref() {
            JsonValueType::Object(fields) => {
                // Array of objects - implement proper streaming solution
                eprintln!("DEBUG ARRAY: Processing array of objects with {} fields", fields.len());

                // Collect all array elements first (we need to know the count for DuckDB)
                let mut object_elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Object => {
                            // Read the object into a temporary structure
                            let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                            object_elements.push(Some(obj_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            object_elements.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            object_elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, object_elements.len());

                // Get the struct child vector for the array elements
                let mut struct_child_vector = list_vector.struct_child(object_elements.len());

                // Insert each object into the struct vector
                for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                    if let Some(field_values) = obj_data {
                        // Insert each field of this object
                        for (field_idx, field) in fields.iter().enumerate() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                            if let Some(field_value) = field_values.get(&field.name) {
                                insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                            } else {
                                field_vector.set_null(elem_idx);
                            }
                        }
                    } else {
                        // Null object - set all fields as null
                        for field_idx in 0..fields.len() {
                            let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                            field_vector.set_null(elem_idx);
                        }
                    }
                }

                eprintln!("DEBUG ARRAY: Successfully processed {} object elements", object_elements.len());
            }
            JsonValueType::Array(nested_element_type) => {
                // Multi-dimensional array - array of arrays
                eprintln!("DEBUG ARRAY: Processing multi-dimensional array with nested element type: {:?}", nested_element_type);

                // Collect all nested arrays using the new recursive approach
                let mut nested_arrays = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Array => {
                            // Process nested array recursively
                            eprintln!("DEBUG ARRAY: Processing nested array element");

                            // Use the new recursive data collection
                            let nested_array_data = collect_nested_array_data_recursive(json_reader, nested_element_type)?;
                            nested_arrays.push(Some(nested_array_data));
                        }
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            nested_arrays.push(None);
                        }
                        _ => {
                            // Skip invalid elements
                            json_reader.skip_value()?;
                            nested_arrays.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // CORRECT APPROACH: Use DuckDB's recursive vector pattern
                // Based on design-elements/duckdb_memory_layout_diagrams.md

                eprintln!("DEBUG ROW: Processing row_idx={}, nested_arrays.len()={}", row_idx, nested_arrays.len());

                // Get the list child vector for nested arrays
                let mut nested_list_vector = list_vector.list_child();

                // CORRECT APPROACH: Store row data for coordinated processing
                // Each row's data needs to be processed with proper cumulative offsets
                // This requires coordination across all rows, not independent processing

                // For now, store this row's data in a global collection
                // TODO: Implement proper multi-row coordination
                eprintln!("DEBUG: Need to implement proper multi-row coordination for cumulative offsets");
                eprintln!("DEBUG: Current row {} has {} nested arrays", row_idx, nested_arrays.len());

                // Temporary single-row processing (will be replaced with proper coordination)
                let _final_offset = insert_multidimensional_array_recursive(&mut nested_list_vector, &nested_arrays, nested_element_type, 0)?;

                // Set up the list entry for this row
                list_vector.set_entry(row_idx, row_idx * nested_arrays.len(), nested_arrays.len());
                eprintln!("DEBUG ROW: Set row {} entry: offset={}, length={}",
                         row_idx, row_idx * nested_arrays.len(), nested_arrays.len());

                eprintln!("DEBUG ARRAY: Successfully processed {} nested arrays", nested_arrays.len());
            }
            _ => {
                // Array of primitives - collect and process
                let mut elements = Vec::new();
                while json_reader.has_next()? {
                    match json_reader.peek()? {
                        struson::reader::ValueType::Null => {
                            json_reader.next_null()?;
                            elements.push(None);
                        }
                        struson::reader::ValueType::Boolean => {
                            let b = json_reader.next_bool()?;
                            elements.push(Some(b.to_string()));
                        }
                        struson::reader::ValueType::Number => {
                            let number_str = json_reader.next_number_as_str()?;
                            elements.push(Some(number_str.to_string()));
                        }
                        struson::reader::ValueType::String => {
                            let s = json_reader.next_string()?;
                            elements.push(Some(s));
                        }
                        _ => {
                            // Skip complex types in primitive arrays
                            json_reader.skip_value()?;
                            elements.push(None);
                        }
                    }
                }

                json_reader.end_array()?;

                // Set up the list entry
                list_vector.set_entry(row_idx, 0, elements.len());

                // Insert primitive elements based on type
                match element_type.as_ref() {
                    JsonValueType::String => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let cstring = std::ffi::CString::new(s.as_str())?;
                                child_vector.insert(i, cstring);
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Number => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let n: f64 = s.parse().unwrap_or(0.0);
                                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = n;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    JsonValueType::Boolean => {
                        let mut child_vector = list_vector.child(elements.len());
                        for (i, elem) in elements.iter().enumerate() {
                            if let Some(s) = elem {
                                let b: bool = s.parse().unwrap_or(false);
                                let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                                data_slice[i] = b;
                            } else {
                                child_vector.set_null(i);
                            }
                        }
                    }
                    _ => {
                        // For other primitive types, set all as null
                        let mut child_vector = list_vector.child(elements.len());
                        for i in 0..elements.len() {
                            child_vector.set_null(i);
                        }
                    }
                }
            }
        }

        Ok(())
    } else {
        Err("Expected array type but got different type".into())
    }
}

// Temporary value type for collecting object data during streaming
#[derive(Debug, Clone)]
enum TempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
}

// Read object from streaming parser into temporary structure
fn read_object_to_temp_structure(
    json_reader: &mut JsonStreamReader<File>,
    fields: &[JsonField],
) -> Result<std::collections::HashMap<String, TempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut field_values = std::collections::HashMap::new();

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();

        // Only collect fields that are in our schema
        if fields.iter().any(|f| f.name == field_name) {
            let temp_value = match json_reader.peek()? {
                struson::reader::ValueType::Null => {
                    json_reader.next_null()?;
                    TempValue::Null
                }
                struson::reader::ValueType::Boolean => {
                    let b = json_reader.next_bool()?;
                    TempValue::Boolean(b)
                }
                struson::reader::ValueType::Number => {
                    let number_str = json_reader.next_number_as_str()?;
                    let number_value: f64 = number_str.parse().unwrap_or(0.0);
                    TempValue::Number(number_value)
                }
                struson::reader::ValueType::String => {
                    let s = json_reader.next_string()?;
                    TempValue::String(s)
                }
                _ => {
                    // For complex types (objects, arrays), skip for now
                    json_reader.skip_value()?;
                    TempValue::Null
                }
            };

            field_values.insert(field_name, temp_value);
        } else {
            // Skip fields not in schema
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;

    Ok(field_values)
}

// Insert temporary value into DuckDB vector
fn insert_temp_value_to_vector(
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    temp_value: &TempValue,
    _expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    match temp_value {
        TempValue::Null => {
            vector.set_null(row_idx);
        }
        TempValue::Boolean(b) => {
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *b;
        }
        TempValue::Number(n) => {
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = *n;
        }
        TempValue::String(s) => {
            eprintln!("DEBUG VECTOR: Inserting string '{}' into vector at row_idx={}", s, row_idx);
            let cstring = std::ffi::CString::new(s.as_str())?;
            vector.insert(row_idx, cstring);
            eprintln!("DEBUG VECTOR: String insertion completed");
        }
    }

    Ok(())
}

// Read object from streaming parser and write directly to DuckDB struct vector
fn read_object_streaming_to_struct_vector(
    json_reader: &mut JsonStreamReader<File>,
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    expected_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    if let JsonValueType::Object(fields) = expected_type {
        let mut struct_vector = output.struct_vector(col_idx);
        read_object_streaming_to_struct_recursive(json_reader, &mut struct_vector, fields, row_idx)
    } else {
        Err("Expected object type but got different type".into())
    }
}

// Recursive helper to read object fields directly into struct vector using streaming
fn read_object_streaming_to_struct_recursive(
    json_reader: &mut JsonStreamReader<File>,
    struct_vector: &mut duckdb::core::StructVector,
    fields: &[JsonField],
    row_idx: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG STREAMING: Reading object to struct with {} fields at row {}", fields.len(), row_idx);

    json_reader.begin_object()?;

    while json_reader.has_next()? {
        let field_name = json_reader.next_name()?.to_string();
        eprintln!("DEBUG STREAMING: Processing struct field: {}", field_name);

        // Find the field in our schema
        if let Some(field_idx) = fields.iter().position(|f| f.name == field_name) {
            let field = &fields[field_idx];
            eprintln!("DEBUG STREAMING: Found field {} at index {}", field_name, field_idx);

            match &field.value_type {
                JsonValueType::Object(nested_fields) => {
                    // Recursive struct handling
                    let mut nested_struct_vector = struct_vector.struct_vector_child(field_idx);
                    if let Err(e) = read_object_streaming_to_struct_recursive(json_reader, &mut nested_struct_vector, nested_fields, 0) {
                        eprintln!("DEBUG STREAMING: Error in nested struct {}: {}", field_name, e);
                        nested_struct_vector.set_null(0);
                    }
                }
                JsonValueType::Array(element_type) => {
                    // Array field handling within STRUCT - implement complete solution
                    eprintln!("DEBUG STREAMING: Processing array field {} with element type: {:?}", field_name, element_type);
                    let mut list_vector = struct_vector.list_vector_child(field_idx);

                    match insert_array_within_struct(json_reader, &mut list_vector, element_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully processed array field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error processing array field {}: {}", field_name, e);
                            list_vector.set_null(0);
                        }
                    }
                }
                _ => {
                    // Primitive field - read directly into child vector
                    let mut field_vector = struct_vector.child(field_idx, 1);
                    match read_primitive_streaming_to_vector(json_reader, &mut field_vector, 0, &field.value_type) {
                        Ok(()) => {
                            eprintln!("DEBUG STREAMING: Successfully inserted primitive field {}", field_name);
                        }
                        Err(e) => {
                            eprintln!("DEBUG STREAMING: Error inserting primitive field {}: {}", field_name, e);
                            field_vector.set_null(0);
                        }
                    }
                }
            }
        } else {
            // Field not in schema - skip it
            eprintln!("DEBUG STREAMING: Skipping unknown field: {}", field_name);
            json_reader.skip_value()?;
        }
    }

    json_reader.end_object()?;
    Ok(())
}

// Enhanced temporary value type that can handle nested arrays
#[derive(Debug, Clone)]
enum NestedTempValue {
    Null,
    Boolean(bool),
    Number(f64),
    String(String),
    Array(Vec<NestedTempValue>), // Recursive array support
}

// Collect nested array data for multi-dimensional arrays with true recursion
fn collect_nested_array_data_recursive(
    json_reader: &mut JsonStreamReader<File>,
    element_type: &JsonValueType,
) -> Result<Vec<NestedTempValue>, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    let mut elements = Vec::new();
    json_reader.begin_array()?;

    while json_reader.has_next()? {
        match json_reader.peek()? {
            struson::reader::ValueType::Null => {
                json_reader.next_null()?;
                elements.push(NestedTempValue::Null);
            }
            struson::reader::ValueType::Boolean => {
                let b = json_reader.next_bool()?;
                elements.push(NestedTempValue::Boolean(b));
            }
            struson::reader::ValueType::Number => {
                let number_str = json_reader.next_number_as_str()?;
                let number_value: f64 = number_str.parse().unwrap_or(0.0);
                elements.push(NestedTempValue::Number(number_value));
            }
            struson::reader::ValueType::String => {
                let s = json_reader.next_string()?;
                elements.push(NestedTempValue::String(s));
            }
            struson::reader::ValueType::Array => {
                // Recursive array processing - handle arbitrary depth
                eprintln!("DEBUG NESTED: Processing deeper nested array");
                if let JsonValueType::Array(deeper_element_type) = element_type {
                    let nested_array = collect_nested_array_data_recursive(json_reader, deeper_element_type)?;
                    elements.push(NestedTempValue::Array(nested_array));
                } else {
                    // Type mismatch - skip
                    json_reader.skip_value()?;
                    elements.push(NestedTempValue::Null);
                }
            }
            _ => {
                // Skip other complex types
                json_reader.skip_value()?;
                elements.push(NestedTempValue::Null);
            }
        }
    }

    json_reader.end_array()?;
    Ok(elements)
}

// Recursive function to insert multi-dimensional arrays following DuckDB's memory layout patterns
// Based on design-elements/duckdb_memory_layout_diagrams.md
fn insert_multidimensional_array_recursive(
    list_vector: &mut duckdb::core::ListVector,
    nested_arrays: &[Option<Vec<NestedTempValue>>],
    element_type: &JsonValueType,
    start_offset: usize,
) -> Result<usize, Box<dyn std::error::Error>> {
    eprintln!("DEBUG RECURSIVE: Processing {} arrays with element_type={:?}, start_offset={}",
              nested_arrays.len(), element_type, start_offset);

    match element_type {
        JsonValueType::Number => {
            // Base case: primitive numbers - use cumulative offset pattern
            let total_elements: usize = nested_arrays.iter()
                .map(|arr_opt| arr_opt.as_ref().map_or(0, |arr| arr.len()))
                .sum();

            if total_elements > 0 {
                // Get child vector with capacity including start_offset
                let total_capacity = start_offset + total_elements;
                let mut child_vector = list_vector.child(total_capacity);
                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_capacity);

                // Insert all data using cumulative offsets starting from start_offset
                let mut current_offset = start_offset;
                for (array_idx, nested_array_opt) in nested_arrays.iter().enumerate() {
                    if let Some(nested_array) = nested_array_opt {
                        // Set list entry with cumulative offset BEFORE inserting data
                        list_vector.set_entry(array_idx, current_offset, nested_array.len());
                        eprintln!("DEBUG RECURSIVE: Set entry[{}] offset={}, length={}",
                                 array_idx, current_offset, nested_array.len());

                        // Insert data for this array
                        for temp_value in nested_array {
                            match temp_value {
                                NestedTempValue::Number(n) => {
                                    data_slice[current_offset] = *n;
                                    eprintln!("DEBUG RECURSIVE: Set data[{}] = {}", current_offset, n);
                                }
                                _ => {
                                    data_slice[current_offset] = 0.0;
                                }
                            }
                            current_offset += 1;
                        }
                    } else {
                        // Null array
                        list_vector.set_null(array_idx);
                        eprintln!("DEBUG RECURSIVE: Set entry[{}] as null", array_idx);
                    }
                }
                Ok(current_offset) // Return the next available offset
            } else {
                Ok(start_offset) // No elements processed
            }
        }
        JsonValueType::Array(deeper_element_type) => {
            // Recursive case: arrays of arrays - use list_child() pattern
            eprintln!("DEBUG RECURSIVE: Handling nested arrays with deeper_element_type={:?}", deeper_element_type);

            // Get the child list vector for the next level down
            let mut child_list_vector = list_vector.list_child();

            // Collect all deeper arrays and calculate cumulative offsets
            let mut all_deeper_arrays = Vec::new();
            let mut cumulative_offset = start_offset;

            for (array_idx, nested_array_opt) in nested_arrays.iter().enumerate() {
                if let Some(nested_array) = nested_array_opt {
                    // Convert NestedTempValue::Array to the format expected by recursive call
                    let deeper_arrays: Vec<Option<Vec<NestedTempValue>>> = nested_array.iter()
                        .map(|temp_val| match temp_val {
                            NestedTempValue::Array(arr) => Some(arr.clone()),
                            _ => None,
                        })
                        .collect();

                    // Set up list entry for this array using cumulative offset
                    list_vector.set_entry(array_idx, cumulative_offset, deeper_arrays.len());
                    eprintln!("DEBUG RECURSIVE: Set parent entry[{}] offset={}, length={}",
                             array_idx, cumulative_offset, deeper_arrays.len());

                    // Add to collection for recursive processing
                    all_deeper_arrays.extend(deeper_arrays);
                    cumulative_offset += nested_array.len();
                } else {
                    // Null array
                    list_vector.set_null(array_idx);
                    eprintln!("DEBUG RECURSIVE: Set parent entry[{}] as null", array_idx);
                }
            }

            // Recursively process all deeper arrays
            if !all_deeper_arrays.is_empty() {
                let final_offset = insert_multidimensional_array_recursive(
                    &mut child_list_vector,
                    &all_deeper_arrays,
                    deeper_element_type,
                    0  // Start from 0 for the child level
                )?;
                Ok(start_offset + final_offset)
            } else {
                Ok(start_offset)
            }
        }
        _ => {
            eprintln!("DEBUG RECURSIVE: Unsupported element type: {:?}", element_type);
            for array_idx in 0..nested_arrays.len() {
                list_vector.set_null(array_idx);
            }
            Ok(start_offset)
        }
    }
}




// CORRECT APPROACH: Process multi-dimensional arrays with proper chunk-level coordination
// This follows DuckDB's streaming pattern: process all rows in current chunk together
fn process_multidimensional_arrays_for_chunk(
    list_vector: &mut duckdb::core::ListVector,
    all_row_data: &[Vec<Option<Vec<NestedTempValue>>>],
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG CHUNK: Processing {} rows with multi-dimensional arrays", all_row_data.len());

    match element_type {
        JsonValueType::Number => {
            // Step 1: Calculate total capacity needed across ALL rows
            let total_child_capacity: usize = all_row_data.iter()
                .map(|row_arrays| {
                    row_arrays.iter()
                        .map(|arr_opt| arr_opt.as_ref().map_or(0, |arr| arr.len()))
                        .sum::<usize>()
                })
                .sum();

            eprintln!("DEBUG CHUNK: Total child capacity needed: {}", total_child_capacity);

            // Step 2: Get child vector with total capacity for entire chunk
            let mut nested_list_vector = list_vector.list_child();

            if total_child_capacity > 0 {
                let mut child_vector = nested_list_vector.child(total_child_capacity);
                let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_child_capacity);

                // Step 3: Insert all data and set up offsets
                let mut global_offset = 0;

                for (row_idx, row_arrays) in all_row_data.iter().enumerate() {
                    let row_start_offset = global_offset;

                    // Insert data for this row
                    for nested_array_opt in row_arrays {
                        if let Some(nested_array) = nested_array_opt {
                            for temp_value in nested_array {
                                match temp_value {
                                    NestedTempValue::Number(n) => {
                                        data_slice[global_offset] = *n;
                                        eprintln!("DEBUG CHUNK: Set data_slice[{}] = {}", global_offset, n);
                                        global_offset += 1;
                                    }
                                    _ => {
                                        data_slice[global_offset] = 0.0;
                                        global_offset += 1;
                                    }
                                }
                            }
                        }
                    }

                    // Set up list entry for this row
                    let row_length = row_arrays.len();
                    list_vector.set_entry(row_idx, row_start_offset, row_length);
                    eprintln!("DEBUG CHUNK: Row {} entry: offset={}, length={}",
                             row_idx, row_start_offset, row_length);

                    // Set up nested array entries within this row
                    let mut row_offset = row_start_offset;
                    for (array_idx, nested_array_opt) in row_arrays.iter().enumerate() {
                        if let Some(nested_array) = nested_array_opt {
                            nested_list_vector.set_entry(row_offset + array_idx, row_offset, nested_array.len());
                            row_offset += nested_array.len();
                        } else {
                            nested_list_vector.set_null(row_offset + array_idx);
                        }
                    }
                }
            }
        }
        _ => {
            eprintln!("DEBUG CHUNK: Unsupported element type for chunk processing");
            // Set all rows as null for unsupported types
            for row_idx in 0..all_row_data.len() {
                list_vector.set_null(row_idx);
            }
        }
    }

    Ok(())
}

// Insert nested array data recursively into list vector with proper offset management
fn insert_nested_array_recursive_with_offset(
    list_vector: &mut duckdb::core::ListVector,
    array_idx: usize,
    offset: usize,
    nested_data: &[NestedTempValue],
    element_type: &JsonValueType,
    total_capacity: usize,
) -> Result<(), Box<dyn std::error::Error>> {
    eprintln!("DEBUG RECURSIVE: array_idx={}, offset={}, nested_data.len()={}, element_type={:?}",
              array_idx, offset, nested_data.len(), element_type);

    // Set up the list entry for this specific nested array with correct offset
    list_vector.set_entry(array_idx, offset, nested_data.len());
    eprintln!("DEBUG RECURSIVE: Set entry for array_idx={} with offset={}, length={}",
              array_idx, offset, nested_data.len());

    match element_type {
        JsonValueType::Array(deeper_element_type) => {
            // Multi-dimensional: each element is another array
            let mut deeper_list_vector = list_vector.list_child();

            // For multi-dimensional arrays, calculate total capacity for deeper level
            let deeper_total_capacity: usize = nested_data.iter()
                .map(|val| match val {
                    NestedTempValue::Array(arr) => arr.len(),
                    _ => 0,
                })
                .sum();

            // For multi-dimensional arrays, we need to calculate offsets for the deeper level too
            let mut deeper_offset = 0;
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Array(deeper_array) => {
                        // Recursively insert the deeper array with proper offset
                        let child_array_idx = offset + elem_idx;
                        insert_nested_array_recursive_with_offset(&mut deeper_list_vector, child_array_idx, deeper_offset, deeper_array, deeper_element_type, deeper_total_capacity)?;
                        deeper_offset += deeper_array.len();
                    }
                    NestedTempValue::Null => {
                        let child_array_idx = offset + elem_idx;
                        deeper_list_vector.set_null(child_array_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        let child_array_idx = offset + elem_idx;
                        deeper_list_vector.set_null(child_array_idx);
                    }
                }
            }
        }
        JsonValueType::Number => {
            // Array of numbers - insert directly with proper capacity and offset
            let mut child_vector = list_vector.child(total_capacity);
            eprintln!("DEBUG RECURSIVE: Got child vector with total_capacity={}", total_capacity);

            // First pass: set null values at correct positions
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                let actual_idx = offset + elem_idx;
                match nested_value {
                    NestedTempValue::Null | _ if !matches!(nested_value, NestedTempValue::Number(_)) => {
                        child_vector.set_null(actual_idx);
                    }
                    _ => {} // Handle numbers in second pass
                }
            }

            // Second pass: set number values using slice with correct offset
            let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(total_capacity);
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                if let NestedTempValue::Number(n) = nested_value {
                    let actual_idx = offset + elem_idx;
                    eprintln!("DEBUG RECURSIVE: Setting data_slice[{}] = {}", actual_idx, n);
                    data_slice[actual_idx] = *n;
                }
            }
        }
        JsonValueType::String => {
            // Array of strings - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::String(s) => {
                        let cstring = std::ffi::CString::new(s.as_str())?;
                        child_vector.insert(elem_idx, cstring);
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        JsonValueType::Boolean => {
            // Array of booleans - insert directly
            let mut child_vector = list_vector.child(nested_data.len());
            for (elem_idx, nested_value) in nested_data.iter().enumerate() {
                match nested_value {
                    NestedTempValue::Boolean(b) => {
                        let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(nested_data.len());
                        data_slice[elem_idx] = *b;
                    }
                    NestedTempValue::Null => {
                        child_vector.set_null(elem_idx);
                    }
                    _ => {
                        // Type mismatch - set as null
                        child_vector.set_null(elem_idx);
                    }
                }
            }
        }
        _ => {
            // For other types, set all as null
            let mut child_vector = list_vector.child(nested_data.len());
            for elem_idx in 0..nested_data.len() {
                child_vector.set_null(elem_idx);
            }
        }
    }

    Ok(())
}

// Insert array within struct context - handles arrays as fields of objects
fn insert_array_within_struct(
    json_reader: &mut JsonStreamReader<File>,
    list_vector: &mut duckdb::core::ListVector,
    element_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader};

    eprintln!("DEBUG ARRAY_IN_STRUCT: Processing array with element type: {:?}", element_type);

    json_reader.begin_array()?;

    match element_type {
        JsonValueType::Object(fields) => {
            // Array of objects within struct - collect and process
            let mut object_elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Object => {
                        let obj_data = read_object_to_temp_structure(json_reader, fields)?;
                        object_elements.push(Some(obj_data));
                    }
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        object_elements.push(None);
                    }
                    _ => {
                        json_reader.skip_value()?;
                        object_elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry for row 0 (since this is within a struct)
            list_vector.set_entry(0, 0, object_elements.len());

            // Get the struct child vector for the array elements
            let struct_child_vector = list_vector.struct_child(object_elements.len());

            // Insert each object into the struct vector
            for (elem_idx, obj_data) in object_elements.iter().enumerate() {
                if let Some(field_values) = obj_data {
                    // Insert each field of this object
                    for (field_idx, field) in fields.iter().enumerate() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());

                        if let Some(field_value) = field_values.get(&field.name) {
                            insert_temp_value_to_vector(&mut field_vector, elem_idx, field_value, &field.value_type)?;
                        } else {
                            field_vector.set_null(elem_idx);
                        }
                    }
                } else {
                    // Null object - set all fields as null
                    for field_idx in 0..fields.len() {
                        let mut field_vector = struct_child_vector.child(field_idx, object_elements.len());
                        field_vector.set_null(elem_idx);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} object elements", object_elements.len());
        }
        JsonValueType::Array(_nested_element_type) => {
            // Multi-dimensional array - array of arrays
            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array detected");

            // For now, implement basic support - count elements and set up structure
            let mut array_count = 0;
            while json_reader.has_next()? {
                // For multi-dimensional arrays, we need more complex handling
                // For now, skip the nested arrays and count them
                json_reader.skip_value()?;
                array_count += 1;
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, array_count);

            // For now, set all nested arrays as null - this needs full implementation
            let mut nested_list_vector = list_vector.list_child();
            for i in 0..array_count {
                nested_list_vector.set_null(i);
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Multi-dimensional array with {} elements (set as null for now)", array_count);
        }
        _ => {
            // Array of primitives within struct
            let mut elements = Vec::new();
            while json_reader.has_next()? {
                match json_reader.peek()? {
                    struson::reader::ValueType::Null => {
                        json_reader.next_null()?;
                        elements.push(None);
                    }
                    struson::reader::ValueType::Boolean => {
                        let b = json_reader.next_bool()?;
                        elements.push(Some(b.to_string()));
                    }
                    struson::reader::ValueType::Number => {
                        let number_str = json_reader.next_number_as_str()?;
                        elements.push(Some(number_str.to_string()));
                    }
                    struson::reader::ValueType::String => {
                        let s = json_reader.next_string()?;
                        elements.push(Some(s));
                    }
                    _ => {
                        json_reader.skip_value()?;
                        elements.push(None);
                    }
                }
            }

            json_reader.end_array()?;

            // Set up the list entry
            list_vector.set_entry(0, 0, elements.len());

            // Insert primitive elements
            match element_type {
                JsonValueType::String => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let cstring = std::ffi::CString::new(s.as_str())?;
                            child_vector.insert(i, cstring);
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Number => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let n: f64 = s.parse().unwrap_or(0.0);
                            let data_slice: &mut [f64] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = n;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                JsonValueType::Boolean => {
                    let mut child_vector = list_vector.child(elements.len());
                    for (i, elem) in elements.iter().enumerate() {
                        if let Some(s) = elem {
                            let b: bool = s.parse().unwrap_or(false);
                            let data_slice: &mut [bool] = child_vector.as_mut_slice_with_len(elements.len());
                            data_slice[i] = b;
                        } else {
                            child_vector.set_null(i);
                        }
                    }
                }
                _ => {
                    // For other types, set all as null
                    let mut child_vector = list_vector.child(elements.len());
                    for i in 0..elements.len() {
                        child_vector.set_null(i);
                    }
                }
            }

            eprintln!("DEBUG ARRAY_IN_STRUCT: Successfully processed {} primitive elements", elements.len());
        }
    }

    Ok(())
}

// Read primitive value from streaming parser and write directly to vector
fn read_primitive_streaming_to_vector(
    json_reader: &mut JsonStreamReader<File>,
    vector: &mut duckdb::core::FlatVector,
    row_idx: usize,
    value_type: &JsonValueType,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match json_reader.peek()? {
        ValueType::Null => {
            json_reader.next_null()?;
            vector.set_null(row_idx);
            Ok(())
        }
        ValueType::Boolean => {
            let b = json_reader.next_bool()?;
            let data_slice: &mut [bool] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = b;
            Ok(())
        }
        ValueType::Number => {
            let number_str = json_reader.next_number_as_str()?;
            let number_value: f64 = number_str.parse().unwrap_or(0.0);
            let data_slice: &mut [f64] = vector.as_mut_slice_with_len(row_idx + 1);
            data_slice[row_idx] = number_value;
            Ok(())
        }
        ValueType::String => {
            let s = json_reader.next_string()?;
            let cstring = std::ffi::CString::new(s)?;
            vector.insert(row_idx, cstring);
            Ok(())
        }
        _ => {
            // For non-primitive types, set as null
            json_reader.skip_value()?;
            vector.set_null(row_idx);
            Ok(())
        }
    }
}

// Helper function to set null values in vectors based on type
fn set_vector_null(
    output: &DataChunkHandle,
    col_idx: usize,
    row_idx: usize,
    value_type: &JsonValueType,
) {
    match value_type {
        JsonValueType::Object(_) => {
            let mut struct_vector = output.struct_vector(col_idx);
            struct_vector.set_null(row_idx);
        }
        JsonValueType::Array(_) => {
            let mut list_vector = output.list_vector(col_idx);
            list_vector.set_null(row_idx);
        }
        _ => {
            let mut flat_vector = output.flat_vector(col_idx);
            flat_vector.set_null(row_idx);
        }
    }
}

// CRITICAL: NEVER convert STRUCT data to VARCHAR - this breaks the core design principle
// Use proper DuckDB STRUCT types and recursive vector handling
// VARCHAR fallbacks are temporary workarounds only for empty objects, not STRUCT data




impl VTab for JsonReaderVTab {
    type InitData = JsonReaderInitData;
    type BindData = JsonReaderBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get the file path parameter
        let file_path = bind.get_parameter(0).to_string();

        eprintln!("DEBUG BIND: File path: {}", file_path);
        eprintln!("DEBUG BIND: Parameter count: {}", bind.get_parameter_count());

        // Try to explore what other information might be available in bind phase
        eprintln!("DEBUG BIND: Exploring BindInfo for additional projection information...");

        // Let's see if BindInfo has any methods that might give us nested field information
        // This is exploratory to understand what's available
        eprintln!("DEBUG BIND: Checking for nested field projection capabilities...");

        // Phase 1: Use two-pass schema inference system
        let inferred_schema = match infer_schema_from_file(&file_path, None) {
            Ok(schema) => {
                eprintln!("DEBUG BIND: Two-pass schema inference completed");
                eprintln!("DEBUG BIND: Root type: {}", schema.root_type.to_compact_string());
                eprintln!("DEBUG BIND: Memory requirements: {} bytes", schema.memory_requirements.total_memory_estimate);
                schema
            },
            Err(e) => {
                eprintln!("DEBUG BIND: Two-pass schema inference failed: {}", e);
                // Fallback to legacy single-pass discovery for compatibility
                eprintln!("DEBUG BIND: Falling back to legacy schema discovery");
                match discover_json_schema(&file_path, None) {
                    Ok(legacy_schema) => {
                        eprintln!("DEBUG BIND: Legacy schema discovery succeeded");
                        // Convert legacy schema to inferred schema format
                        let root_type = InferredJsonType::from_legacy_json_type(&legacy_schema.root_type);
                        InferredSchema {
                            root_type,
                            statistics: SchemaStatistics {
                                total_rows: 1,
                                max_nesting_depth: 1,
                                unique_field_names: legacy_schema.columns.iter().map(|c| c.name.clone()).collect(),
                                schema_complexity_score: legacy_schema.columns.len(),
                            },
                            memory_requirements: MemoryRequirements {
                                vector_capacities: HashMap::new(),
                                total_memory_estimate: 1024 * 1024, // 1MB default
                                peak_memory_estimate: 2 * 1024 * 1024, // 2MB default
                            },
                        }
                    },
                    Err(legacy_e) => {
                        eprintln!("DEBUG BIND: Both schema inference methods failed");
                        return Err(format!("Schema inference failed: {} (fallback: {})", e, legacy_e).into());
                    }
                }
            }
        };

        // Convert inferred schema to legacy JsonSchema format for compatibility
        let full_schema = JsonSchema {
            root_type: inferred_schema.root_type.to_legacy_json_type(),
            columns: generate_columns_from_inferred_schema(&inferred_schema.root_type),
        };

        // At bind time, we don't have projection info yet, so use full schema
        // Projection optimization will be applied during data reading
        eprintln!("DEBUG BIND: Using full schema at bind time (projection applied later)");

        // Add result columns to DuckDB based on full schema
        for (i, column) in full_schema.columns.iter().enumerate() {
            eprintln!("DEBUG BIND: Adding column '{}' at index {}", column.name, i);

            // Convert JSON types to DuckDB logical types (including STRUCT/ARRAY)
            let logical_type = json_type_to_duckdb_type(&column.value_type)?;

            bind.add_result_column(&column.name, logical_type);
            eprintln!("DEBUG BIND: Added '{}' as {:?} type", column.name, column.value_type);
        }

        Ok(JsonReaderBindData {
            file_path,
            schema: full_schema,
        })
    }

    fn init(init: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        // Test projection pushdown capabilities
        eprintln!("DEBUG: Testing projection pushdown capabilities");

        // Get column indices for projection pushdown
        let column_indices = init.get_column_indices();
        eprintln!("DEBUG: Projected column indices: {:?}", column_indices);
        eprintln!("DEBUG: Number of projected columns: {}", column_indices.len());

        // Get bind data to access column names
        let bind_data = unsafe { &*(init.get_bind_data() as *const JsonReaderBindData) };
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG: All available columns: {:?}", column_names);

        // Map indices to actual column names
        let projected_column_names: Vec<String> = column_indices
            .iter()
            .map(|&idx| {
                if (idx as usize) < column_names.len() {
                    column_names[idx as usize].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", idx)
                }
            })
            .collect();

        eprintln!("DEBUG: Projected column names: {:?}", projected_column_names);

        // Try to explore what other methods might be available on InitInfo
        eprintln!("DEBUG: Exploring InitInfo methods...");

        // Let's try to see if there are any other methods we can call on InitInfo
        // This is exploratory - we'll see what's available

        // Check if there's any way to get more detailed projection information
        eprintln!("DEBUG: Checking for additional projection information...");

        // Let's see if there are any other methods we can call
        // (This is exploratory - some might not exist)

        if column_indices.is_empty() {
            eprintln!("DEBUG: No specific columns projected (SELECT * query?)");
        } else {
            eprintln!("DEBUG: Specific columns requested: {:?}", projected_column_names);
        }

        // Convert column indices to simple usize vector
        let projected_columns: Vec<usize> = column_indices
            .iter()
            .map(|&idx| idx as usize)
            .collect();

        Ok(JsonReaderInitData {
            current_element: AtomicUsize::new(0),
            finished: AtomicBool::new(false),
            projected_columns,
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = unsafe { &*(func.get_bind_data() as *const JsonReaderBindData) };

        if init_data.finished.load(Ordering::Relaxed) {
            output.set_len(0);
            return Ok(());
        }

        eprintln!("DEBUG FUNC: Processing file: {}", bind_data.file_path);
        let column_names: Vec<String> = bind_data.schema.columns.iter().map(|c| c.name.clone()).collect();
        eprintln!("DEBUG FUNC: Available columns: {:?}", column_names);

        // Apply projection optimization based on projected columns
        let projected_column_names: Vec<String> = init_data.projected_columns
            .iter()
            .map(|&col_idx| {
                if col_idx < column_names.len() {
                    column_names[col_idx].clone()
                } else {
                    format!("UNKNOWN_COLUMN_{}", col_idx)
                }
            })
            .collect();

        eprintln!("DEBUG FUNC: Projected columns for optimization: {:?}", projected_column_names);

        // Apply projection pushdown to schema
        let optimized_schema = apply_projection_pushdown(&bind_data.schema, &projected_column_names);
        eprintln!("DEBUG FUNC: Using optimized schema: {:?}", optimized_schema);

        // Read JSON file using pure streaming and write directly to DuckDB vectors
        eprintln!("DEBUG FUNC: Reading JSON file with pure streaming to vectors");

        match read_json_streaming_to_vectors(&bind_data.file_path, &optimized_schema, output) {
            Ok(row_count) => {
                eprintln!("DEBUG FUNC: Successfully processed {} rows using pure streaming", row_count);

                if row_count == 0 {
                    output.set_len(0);
                } else {
                    output.set_len(row_count);
                    eprintln!("DEBUG FUNC: Set output length to {} rows", row_count);
                }

                // Mark as finished after processing the data
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished");
            }
            Err(e) => {
                eprintln!("ERROR: Failed to read JSON file '{}': {}", bind_data.file_path, e);

                // Provide helpful error messages based on error type
                let error_msg = if e.to_string().contains("not found") {
                    format!("JSON file not found: {}", bind_data.file_path)
                } else if e.to_string().contains("malformed") || e.to_string().contains("Failed to read") {
                    format!("Malformed JSON in file: {} - {}", bind_data.file_path, e)
                } else if e.to_string().contains("permission") || e.to_string().contains("Permission denied") {
                    format!("Permission denied reading file: {}", bind_data.file_path)
                } else if e.to_string().contains("too many fields") {
                    format!("JSON object too complex in file: {} - {}", bind_data.file_path, e)
                } else {
                    format!("Error reading JSON file '{}': {}", bind_data.file_path, e)
                };

                output.set_len(0);
                init_data.finished.store(true, Ordering::Relaxed);
                eprintln!("DEBUG FUNC: Marked processing as finished due to error");
                return Err(error_msg.into());
            }
        }

        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonReaderVTab>("streaming_json_reader")
        .expect("Failed to register streaming JSON reader table function");
    Ok(())
}