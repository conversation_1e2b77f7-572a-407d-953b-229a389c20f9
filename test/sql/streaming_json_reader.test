# name: test/sql/streaming_json_reader.test
# description: test streaming_json_reader extension
# group: [json]

# Before we load the extension, this will fail
statement error
SELECT * FROM streaming_json_reader('test.json');
----
Catalog Error: Table Function with name streaming_json_reader does not exist!

# Require statement will ensure the extension is loaded from now on
require streaming_json_reader

require icu

# Create a test JSON file with nested array
statement ok
COPY (SELECT '{"users": [{"id": 1, "name": "<PERSON>"}, {"id": 2, "name": "<PERSON>"}]}') TO 'test_nested.json' (FORMAT CSV, QUOTE '', HEADER false);

# Test array flattening - should return rows for each user
query III
SELECT * FROM streaming_json_reader('test_nested.json', 'users');
----
1	Alice
2	Bob